/**
 * 阿里云语音识别服务
 */
import { ALIYUN_CONFIG } from './config';
import { getToken } from './token';

// 语音识别选项
export interface SpeechRecognitionOptions {
  format?: string;
  sampleRate?: number;
  enablePunctuation?: boolean;
  enableITN?: boolean;
  enableSemanticSentenceDetection?: boolean;
}

// 语音识别结果
export interface SpeechRecognitionResult {
  success: boolean;
  text?: string;
  errorMessage?: string;
  requestId?: string;
  confidence?: number;
  duration?: number;
  emotionTag?: string;
  emotionConfidence?: number;
}

/**
 * 一句话识别
 * 适用于60秒以内的短音频
 *
 * @param audioData 音频数据（Buffer）
 * @param options 识别选项
 * @returns 识别结果
 */
export async function recognizeUtterance(
  audioData: Buffer,
  options: SpeechRecognitionOptions = {}
): Promise<SpeechRecognitionResult> {
  // 在服务器端使用RESTful API
  if (typeof window === 'undefined') {
    return recognizeUtteranceServer(audioData, options);
  } else {
    // 在客户端直接调用API
    return recognizeUtteranceClient(audioData, options);
  }
}

/**
 * 服务器端一句话识别实现
 * 使用RESTful API
 */
async function recognizeUtteranceServer(
  audioData: Buffer,
  options: SpeechRecognitionOptions = {}
): Promise<SpeechRecognitionResult> {
  try {
    console.log('===== 开始服务器端语音识别 =====');
    console.log('音频数据大小:', audioData.length, '字节');

    // 检查WAV文件头（如果是WAV格式）
    if (options.format === 'wav' || ALIYUN_CONFIG.defaults.format === 'wav') {
      checkWavHeader(audioData);
    }

    // 获取Token
    const token = await getToken();
    console.log('获取到Token:', token.substring(0, 10) + '...');

    // 构建URL参数
    let url = ALIYUN_CONFIG.endpoints.nlsRest;
    url += `?appkey=${ALIYUN_CONFIG.appKey}`;
    url += `&format=${options.format || ALIYUN_CONFIG.defaults.format}`;
    url += `&sample_rate=${options.sampleRate || ALIYUN_CONFIG.defaults.sampleRate}`;

    if (options.enablePunctuation !== undefined) {
      url += `&enable_punctuation_prediction=${options.enablePunctuation}`;
    } else if (ALIYUN_CONFIG.defaults.enablePunctuation) {
      url += `&enable_punctuation_prediction=true`;
    }

    if (options.enableITN !== undefined) {
      url += `&enable_inverse_text_normalization=${options.enableITN}`;
    } else if (ALIYUN_CONFIG.defaults.enableITN) {
      url += `&enable_inverse_text_normalization=true`;
    }

    if (options.enableSemanticSentenceDetection !== undefined) {
      url += `&enable_semantic_sentence_detection=${options.enableSemanticSentenceDetection}`;
    }

    console.log('请求URL:', url);
    console.log('请求参数:', {
      format: options.format || ALIYUN_CONFIG.defaults.format,
      sampleRate: options.sampleRate || ALIYUN_CONFIG.defaults.sampleRate,
      enablePunctuation: options.enablePunctuation !== undefined ? options.enablePunctuation : ALIYUN_CONFIG.defaults.enablePunctuation,
      enableITN: options.enableITN !== undefined ? options.enableITN : ALIYUN_CONFIG.defaults.enableITN,
      enableSemanticSentenceDetection: options.enableSemanticSentenceDetection
    });

    // 使用axios发送请求
    const axios = require('axios');

    console.log('发送POST请求到阿里云API...');
    // 发送POST请求
    const response = await axios.post(url, audioData, {
      headers: {
        'X-NLS-Token': token,
        'Content-Type': 'application/octet-stream',
      },
    });

    // 解析响应
    const result = response.data;
    console.log('阿里云API响应状态码:', response.status);
    console.log('阿里云API完整响应:', JSON.stringify(result, null, 2));

    if (response.status === 200 && result.status === 20000000) {
      console.log('识别成功, 结果文本:', result.result || '(空)');
      return {
        success: true,
        text: result.result || '',
        requestId: result.task_id,
      };
    } else {
      console.error('识别失败:', result.message || result.status);
      return {
        success: false,
        errorMessage: `识别失败: ${result.message || result.status}`,
        requestId: result.task_id,
      };
    }
  } catch (error) {
    console.error('语音识别失败:', error);
    return {
      success: false,
      errorMessage: `语音识别错误: ${(error as Error).message}`,
    };
  } finally {
    console.log('===== 结束服务器端语音识别 =====');
  }
}

/**
 * 客户端一句话识别实现
 * 通过API调用服务器端接口
 */
async function recognizeUtteranceClient(
  audioData: Buffer,
  options: SpeechRecognitionOptions = {}
): Promise<SpeechRecognitionResult> {
  try {
    // 创建FormData
    const formData = new FormData();
    const blob = new Blob([audioData], { type: 'audio/wav' });
    formData.append('audio', blob, 'recording.wav');
    formData.append('format', options.format || ALIYUN_CONFIG.defaults.format);
    formData.append('sampleRate', String(options.sampleRate || ALIYUN_CONFIG.defaults.sampleRate));

    if (options.enablePunctuation !== undefined) {
      formData.append('enablePunctuation', String(options.enablePunctuation));
    }

    if (options.enableITN !== undefined) {
      formData.append('enableITN', String(options.enableITN));
    }

    if (options.enableSemanticSentenceDetection !== undefined) {
      formData.append('enableSemanticSentenceDetection', String(options.enableSemanticSentenceDetection));
    }

    // 发送请求
    const response = await fetch('/api/speech', {
      method: 'POST',
      body: formData,
    });

    const result = await response.json();

    if (result.success) {
      return {
        success: true,
        text: result.text || '',
        requestId: result.requestId,
        confidence: result.confidence,
        duration: result.duration,
        emotionTag: result.emotionTag,
        emotionConfidence: result.emotionConfidence
      };
    } else {
      return {
        success: false,
        errorMessage: result.message || '识别失败',
        requestId: result.requestId
      };
    }
  } catch (error) {
    console.error('语音识别失败:', error);
    return {
      success: false,
      errorMessage: `语音识别错误: ${(error as Error).message}`,
    };
  }
}

/**
 * 将音频文件转换为Base64编码
 *
 * @param buffer 音频文件的Buffer
 * @returns Base64编码的字符串
 */
export function audioToBase64(buffer: Buffer): string {
  return buffer.toString('base64');
}

/**
 * 检查WAV文件头信息
 *
 * @param buffer 音频数据Buffer
 * @returns 是否是有效的WAV文件
 */
function checkWavHeader(buffer: Buffer): boolean {
  if (buffer.length < 44) {
    console.error('WAV文件头不完整，长度小于44字节');
    return false;
  }

  // 检查RIFF头
  const riff = buffer.slice(0, 4).toString('ascii');
  const wave = buffer.slice(8, 12).toString('ascii');
  const fmt = buffer.slice(12, 16).toString('ascii');

  console.log('WAV文件头标识:', { riff, wave, fmt });

  if (riff !== 'RIFF' || wave !== 'WAVE' || fmt !== 'fmt ') {
    console.error('WAV文件头标识不正确:', { riff, wave, fmt });
    return false;
  }

  // 读取音频格式信息
  const audioFormat = buffer.readUInt16LE(20);      // 1表示PCM
  const numChannels = buffer.readUInt16LE(22);      // 通道数
  const sampleRate = buffer.readUInt32LE(24);       // 采样率
  const byteRate = buffer.readUInt32LE(28);         // 字节率
  const blockAlign = buffer.readUInt16LE(32);       // 块对齐
  const bitsPerSample = buffer.readUInt16LE(34);    // 位深度

  console.log('WAV文件头信息:');
  console.log('- 音频格式:', audioFormat, audioFormat === 1 ? '(PCM)' : '(非PCM)');
  console.log('- 通道数:', numChannels, numChannels === 1 ? '(单声道)' : '(多声道)');
  console.log('- 采样率:', sampleRate, 'Hz');
  console.log('- 字节率:', byteRate, 'bytes/sec');
  console.log('- 块对齐:', blockAlign, 'bytes');
  console.log('- 位深度:', bitsPerSample, 'bits');

  // 检查是否符合阿里云要求
  if (audioFormat !== 1) {
    console.warn('警告: 音频不是PCM格式，阿里云可能无法正确识别');
  }

  if (numChannels !== 1) {
    console.warn('警告: 音频不是单声道，阿里云要求单声道音频');
  }

  if (sampleRate !== 8000 && sampleRate !== 16000) {
    console.warn('警告: 采样率不是8000Hz或16000Hz，阿里云只支持这两种采样率');
  }

  if (bitsPerSample !== 16) {
    console.warn('警告: 位深度不是16bit，阿里云要求16bit采样位数');
  }

  return true;
}
