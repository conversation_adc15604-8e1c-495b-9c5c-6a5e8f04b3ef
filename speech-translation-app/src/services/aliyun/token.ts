/**
 * 阿里云Token管理模块
 */
import RPCClient from '@alicloud/pop-core';
import { ALIYUN_CONFIG } from './config';

// Token缓存
interface TokenCache {
  id: string;
  expireTime: number;
}

let tokenCache: TokenCache | null = null;

// 创建RPC客户端
const createClient = () => {
  return new RPCClient({
    accessKeyId: ALIYUN_CONFIG.accessKeyId,
    accessKeySecret: ALIYUN_CONFIG.accessKeySecret,
    endpoint: ALIYUN_CONFIG.endpoints.nls,
    apiVersion: ALIYUN_CONFIG.apiVersion.nls,
  });
};

/**
 * 获取访问Token
 * 自动缓存Token并在过期前刷新
 */
export async function getToken(): Promise<string> {
  // 检查缓存的Token是否有效
  const now = Date.now();
  if (tokenCache && tokenCache.expireTime > now + 60000) {
    return tokenCache.id;
  }

  try {
    const client = createClient();

    // 调用阿里云API创建Token
    const response = await client.request('CreateToken', {}, {
      method: 'POST',
    }) as { Token?: { Id?: string, ExpireTime?: number } };

    if (!response || !response.Token || !response.Token.Id) {
      throw new Error('获取Token失败：无效的响应');
    }

    // 缓存Token
    tokenCache = {
      id: response.Token.Id,
      // 将过期时间转换为毫秒并留出5分钟的安全边界
      expireTime: now + (response.Token.ExpireTime * 1000) - 300000,
    };

    return tokenCache.id;
  } catch (error) {
    console.error('获取阿里云Token失败:', error);
    throw new Error(`获取Token失败: ${(error as Error).message}`);
  }
}
