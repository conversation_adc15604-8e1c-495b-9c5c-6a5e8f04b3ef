/**
 * 阿里云服务配置
 */

// 从环境变量获取配置
export const ALIYUN_CONFIG = {
  accessKeyId: process.env.ALIYUN_ACCESS_KEY_ID || '',
  accessKeySecret: process.env.ALIYUN_ACCESS_KEY_SECRET || '',
  appKey: process.env.ALIYUN_APPKEY || '',
  region: process.env.ALIYUN_API_REGION || 'cn-shanghai',

  // API端点
  endpoints: {
    // 一句话识别RESTful API地址
    nlsRest: `https://nls-gateway-${process.env.ALIYUN_API_REGION || 'cn-shanghai'}.aliyuncs.com/stream/v1/asr`,
    // Token服务地址
    nls: 'https://nls-meta.cn-shanghai.aliyuncs.com',
  },

  // API版本
  apiVersion: {
    nls: '2019-02-28',
  },

  // 默认参数
  defaults: {
    format: 'wav',
    sampleRate: 16000,
    enablePunctuation: true,
    enableITN: true, // Inverse Text Normalization
  },
};

// 验证配置是否完整
export function validateConfig(): boolean {
  const { accessKeyId, accessKeySecret, appKey } = ALIYUN_CONFIG;

  if (!accessKeyId || !accessKeySecret || !appKey) {
    console.error('阿里云配置不完整，请检查环境变量');
    return false;
  }

  return true;
}
