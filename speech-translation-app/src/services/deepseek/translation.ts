/**
 * DeepSeek翻译服务
 */

import axios from 'axios';

// 支持的语言
export const SUPPORTED_LANGUAGES = {
  'zh': '中文',
  'en': '英语',
  'ja': '日语',
  'ko': '韩语',
  'fr': '法语',
  'de': '德语',
  'es': '西班牙语',
  'ru': '俄语',
  'pt': '葡萄牙语',
  'it': '意大利语',
  'ar': '阿拉伯语',
  'hi': '印地语',
  'vi': '越南语',
  'th': '泰语',
  'id': '印尼语',
  'ms': '马来语',
};

// 翻译选项接口
export interface TranslationOptions {
  sourceLanguage: string;
  targetLanguage: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
}

// 翻译结果接口
export interface TranslationResult {
  success: boolean;
  translatedText?: string;
  errorMessage?: string;
  requestId?: string;
  sourceLanguage?: string;
  targetLanguage?: string;
  originalText?: string;
}

// DeepSeek配置
const DEEPSEEK_CONFIG = {
  apiKey: process.env.DEEPSEEK_API_KEY || '',
  baseUrl: process.env.DEEPSEEK_API_BASE_URL || 'https://api.deepseek.com',
  defaultModel: 'deepseek-chat',
  defaultTemperature: 0.3,
  defaultMaxTokens: 2000,
};

/**
 * 验证DeepSeek配置
 * @returns 配置是否有效
 */
export function validateConfig(): boolean {
  return !!DEEPSEEK_CONFIG.apiKey;
}

/**
 * 使用DeepSeek API翻译文本
 * 
 * @param text 要翻译的文本
 * @param options 翻译选项
 * @returns 翻译结果
 */
export async function translateText(
  text: string,
  options: TranslationOptions
): Promise<TranslationResult> {
  try {
    if (!validateConfig()) {
      return {
        success: false,
        errorMessage: 'DeepSeek API配置不完整，请检查环境变量',
      };
    }

    if (!text || text.trim() === '') {
      return {
        success: false,
        errorMessage: '翻译文本不能为空',
      };
    }

    // 构建提示词
    const prompt = buildTranslationPrompt(text, options);

    // 调用DeepSeek API
    const response = await axios.post(
      `${DEEPSEEK_CONFIG.baseUrl}/v1/chat/completions`,
      {
        model: options.model || DEEPSEEK_CONFIG.defaultModel,
        messages: [
          {
            role: 'system',
            content: '你是一个专业的翻译助手，请将用户提供的文本翻译成目标语言，保持原文的意思、风格和格式。只返回翻译结果，不要添加任何解释或额外内容。',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: options.temperature || DEEPSEEK_CONFIG.defaultTemperature,
        max_tokens: options.maxTokens || DEEPSEEK_CONFIG.defaultMaxTokens,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${DEEPSEEK_CONFIG.apiKey}`,
        },
      }
    );

    // 解析响应
    const result = response.data;
    const translatedText = result.choices[0]?.message?.content?.trim();

    if (translatedText) {
      return {
        success: true,
        translatedText,
        sourceLanguage: options.sourceLanguage,
        targetLanguage: options.targetLanguage,
        originalText: text,
        requestId: result.id,
      };
    } else {
      return {
        success: false,
        errorMessage: '翻译结果为空',
        requestId: result.id,
      };
    }
  } catch (error) {
    console.error('翻译失败:', error);
    return {
      success: false,
      errorMessage: `翻译错误: ${(error as Error).message}`,
    };
  }
}

/**
 * 构建翻译提示词
 * 
 * @param text 要翻译的文本
 * @param options 翻译选项
 * @returns 提示词
 */
function buildTranslationPrompt(text: string, options: TranslationOptions): string {
  const sourceLangName = SUPPORTED_LANGUAGES[options.sourceLanguage] || options.sourceLanguage;
  const targetLangName = SUPPORTED_LANGUAGES[options.targetLanguage] || options.targetLanguage;
  
  return `请将以下${sourceLangName}文本翻译成${targetLangName}：\n\n${text}\n\n只需返回翻译结果，不要添加任何解释或额外内容。`;
}

/**
 * 检测文本语言
 * 
 * @param text 要检测的文本
 * @returns 检测到的语言代码
 */
export async function detectLanguage(text: string): Promise<string> {
  try {
    if (!validateConfig()) {
      throw new Error('DeepSeek API配置不完整，请检查环境变量');
    }

    if (!text || text.trim() === '') {
      throw new Error('检测文本不能为空');
    }

    // 调用DeepSeek API
    const response = await axios.post(
      `${DEEPSEEK_CONFIG.baseUrl}/v1/chat/completions`,
      {
        model: DEEPSEEK_CONFIG.defaultModel,
        messages: [
          {
            role: 'system',
            content: '你是一个语言检测助手，请检测用户提供的文本是什么语言，只返回语言代码，例如：中文返回"zh"，英语返回"en"，日语返回"ja"等。',
          },
          {
            role: 'user',
            content: `请检测以下文本的语言，只返回语言代码（如zh、en、ja等）：\n\n${text}`,
          },
        ],
        temperature: 0.1,
        max_tokens: 10,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${DEEPSEEK_CONFIG.apiKey}`,
        },
      }
    );

    // 解析响应
    const result = response.data;
    const detectedLanguage = result.choices[0]?.message?.content?.trim().toLowerCase();

    // 验证语言代码
    if (detectedLanguage && SUPPORTED_LANGUAGES[detectedLanguage]) {
      return detectedLanguage;
    } else {
      // 默认返回中文
      return 'zh';
    }
  } catch (error) {
    console.error('语言检测失败:', error);
    // 默认返回中文
    return 'zh';
  }
}
