'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { toast } from 'sonner';
import { useAudioStore } from '@/store/audioStore';

export function AudioUploadTest() {
  const { audioBlob, audioUrl, resetRecording } = useAudioStore();
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadResult, setUploadResult] = useState<{
    success: boolean;
    message: string;
    downloadUrl?: string;
    size?: number;
    uploadTime?: number;
  } | null>(null);

  // 上传音频
  const handleUpload = async () => {
    if (!audioBlob) {
      toast.error('没有可上传的音频，请先录制音频');
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);
    setUploadResult(null);

    // 使用局部变量而不是状态来跟踪开始时间
    const startTime = Date.now();
    console.log('Upload start time:', startTime);

    try {
      // 创建FormData
      const formData = new FormData();
      formData.append('audio', audioBlob, 'recording.wav');

      // 使用fetch API上传
      const xhr = new XMLHttpRequest();
      xhr.open('POST', '/api/audio', true);

      // 进度监听
      xhr.upload.onprogress = (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100);
          setUploadProgress(progress);
        }
      };

      // 完成处理
      xhr.onload = () => {
        const uploadTime = Date.now() - startTime;
        console.log('Upload end time:', Date.now(), 'Duration:', uploadTime);
        if (xhr.status >= 200 && xhr.status < 300) {
          const response = JSON.parse(xhr.responseText);
          if (response.success) {
            toast.success('音频上传成功');
            setUploadResult({
              success: true,
              message: '上传成功',
              downloadUrl: response.downloadUrl,
              size: audioBlob.size,
              uploadTime
            });

            // 发送测试指标事件
            const testEvent = new CustomEvent('audioUploadComplete', {
              detail: {
                audioSize: audioBlob.size,
                duration: audioBlob.size / (16000 * 2), // 估算16kHz, 16bit的时长
                uploadTime,
                success: true
              }
            });
            window.dispatchEvent(testEvent);
          } else {
            toast.error(`上传失败: ${response.message}`);
            setUploadResult({
              success: false,
              message: `上传失败: ${response.message}`,
              uploadTime
            });

            // 发送测试指标事件
            const testEvent = new CustomEvent('audioUploadComplete', {
              detail: {
                audioSize: audioBlob.size,
                duration: audioBlob.size / (16000 * 2),
                uploadTime,
                success: false,
                error: response.message
              }
            });
            window.dispatchEvent(testEvent);
          }
        } else {
          toast.error(`上传失败: ${xhr.status}`);
          setUploadResult({
            success: false,
            message: `上传失败: 服务器返回 ${xhr.status}`,
            uploadTime
          });

          // 发送测试指标事件
          const testEvent = new CustomEvent('audioUploadComplete', {
            detail: {
              audioSize: audioBlob.size,
              duration: audioBlob.size / (16000 * 2),
              uploadTime,
              success: false,
              error: `服务器返回 ${xhr.status}`
            }
          });
          window.dispatchEvent(testEvent);
        }
        setIsUploading(false);
      };

      // 错误处理
      xhr.onerror = () => {
        const uploadTime = Date.now() - startTime;
        toast.error('上传过程中发生网络错误');
        setUploadResult({
          success: false,
          message: '网络错误',
          uploadTime
        });

        // 发送测试指标事件
        const testEvent = new CustomEvent('audioUploadComplete', {
          detail: {
            audioSize: audioBlob.size,
            duration: audioBlob.size / (16000 * 2),
            uploadTime,
            success: false,
            error: '网络错误'
          }
        });
        window.dispatchEvent(testEvent);

        setIsUploading(false);
      };

      // 发送请求
      xhr.send(formData);
    } catch (error) {
      const uploadTime = Date.now() - startTime;
      console.error('上传错误:', error);
      toast.error('上传过程中发生错误');
      setUploadResult({
        success: false,
        message: `错误: ${(error as Error).message}`,
        uploadTime
      });

      // 发送测试指标事件
      const testEvent = new CustomEvent('audioUploadComplete', {
        detail: {
          audioSize: audioBlob.size,
          duration: audioBlob.size / (16000 * 2),
          uploadTime,
          success: false,
          error: (error as Error).message
        }
      });
      window.dispatchEvent(testEvent);

      setIsUploading(false);
    }
  };

  // 重置
  const handleReset = () => {
    resetRecording();
    setUploadResult(null);
    setUploadProgress(0);
  };

  return (
    <Card className="w-full max-w-md mx-auto mt-6">
      <CardHeader>
        <CardTitle className="text-center">音频上传测试</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 音频信息 */}
        {audioBlob && (
          <div className="text-sm space-y-1">
            <p>音频大小: {(audioBlob.size / 1024 / 1024).toFixed(2)} MB</p>
            <p>音频类型: {audioBlob.type || 'audio/wav'}</p>
          </div>
        )}

        {/* 上传进度 */}
        {isUploading && (
          <div className="space-y-2">
            <p className="text-sm text-center">上传中... {uploadProgress}%</p>
            <Progress value={uploadProgress} className="h-2" />
          </div>
        )}

        {/* 上传结果 */}
        {uploadResult && (
          <div className={`p-3 rounded-md ${uploadResult.success ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'}`}>
            <p className="font-medium">{uploadResult.message}</p>
            {uploadResult.uploadTime && (
              <p className="text-sm">
                上传耗时: {
                  uploadResult.uploadTime < 1000
                    ? `${uploadResult.uploadTime} 毫秒`
                    : `${(uploadResult.uploadTime / 1000).toFixed(2)} 秒`
                }
              </p>
            )}
            {uploadResult.size && (
              <p className="text-sm">文件大小: {(uploadResult.size / 1024 / 1024).toFixed(2)} MB</p>
            )}
          </div>
        )}

        {/* 音频播放器 - 原始录音 */}
        {audioUrl && (
          <div className="space-y-2">
            <p className="text-sm font-medium">原始录音:</p>
            <audio src={audioUrl} controls className="w-full" />
          </div>
        )}

        {/* 音频播放器 - 从服务器下载的音频 */}
        {uploadResult?.downloadUrl && (
          <div className="space-y-2">
            <p className="text-sm font-medium">从服务器下载的音频:</p>
            <audio src={uploadResult.downloadUrl} controls className="w-full" />
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-center space-x-2">
        {audioBlob && !isUploading && (
          <Button onClick={handleUpload} disabled={isUploading}>
            上传音频
          </Button>
        )}
        {uploadResult && (
          <Button variant="outline" onClick={handleReset}>
            重置
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
