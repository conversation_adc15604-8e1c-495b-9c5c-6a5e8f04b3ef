/**
 * 翻译面板组件
 */

'use client';

import React, { useEffect, useState } from 'react';
import { useTranslationStore, SUPPORTED_LANGUAGES } from '@/store/translationStore';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';

/**
 * 翻译面板组件
 */
export default function TranslationPanel() {
  // 使用翻译状态
  const {
    isTranslating,
    sourceText,
    translatedText,
    sourceLanguage,
    targetLanguage,
    detectedLanguage,
    autoDetectLanguage,
    error,
    setSourceText,
    setSourceLanguage,
    setTargetLanguage,
    setAutoDetectLanguage,
    translate,
    clearTranslation,
    swapLanguages,
  } = useTranslationStore();

  // 处理源文本变化
  const handleSourceTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setSourceText(e.target.value);
  };

  // 处理复制翻译结果
  const handleCopyTranslation = () => {
    if (translatedText) {
      navigator.clipboard.writeText(translatedText);
      toast.success('已复制到剪贴板');
    }
  };

  // 处理自动检测语言切换
  const handleAutoDetectChange = (checked: boolean) => {
    setAutoDetectLanguage(checked);
  };

  return (
    <div className="flex flex-col space-y-4 w-full max-w-4xl mx-auto p-4">
      {/* 语言选择栏 */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <Select
            value={sourceLanguage}
            onValueChange={setSourceLanguage}
            disabled={autoDetectLanguage}
          >
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="选择源语言" />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(SUPPORTED_LANGUAGES).map(([code, name]) => (
                <SelectItem key={code} value={code}>
                  {name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <div className="flex items-center">
            <Switch
              checked={autoDetectLanguage}
              onCheckedChange={handleAutoDetectChange}
              className="mr-2"
            />
            <span className="text-sm text-gray-600">自动检测</span>
          </div>
        </div>

        <Button
          onClick={swapLanguages}
          size="sm"
          variant="ghost"
          aria-label="交换语言"
          title="交换语言"
        >
          ↔️
        </Button>

        <div className="flex items-center">
          <Select
            value={targetLanguage}
            onValueChange={setTargetLanguage}
          >
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="选择目标语言" />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(SUPPORTED_LANGUAGES).map(([code, name]) => (
                <SelectItem key={code} value={code}>
                  {name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* 源文本输入 */}
      <div className="relative">
        <Textarea
          value={sourceText}
          onChange={handleSourceTextChange}
          placeholder="请输入要翻译的文本"
          className="min-h-[150px] resize-y"
        />
        {sourceText && (
          <Button
            className="absolute bottom-2 right-2"
            size="sm"
            variant="ghost"
            onClick={() => setSourceText('')}
          >
            清空
          </Button>
        )}
      </div>

      {/* 操作按钮 */}
      <div className="flex justify-center space-x-2">
        <Button
          onClick={translate}
          disabled={isTranslating || !sourceText.trim()}
          className="bg-blue-500 hover:bg-blue-600 text-white"
        >
          {isTranslating ? '翻译中...' : '翻译'}
        </Button>
        <Button
          onClick={clearTranslation}
          variant="outline"
          disabled={!sourceText && !translatedText}
        >
          清空
        </Button>
      </div>

      {/* 错误信息 */}
      {error && (
        <div className="text-red-500 text-center py-2">
          {error}
        </div>
      )}

      {/* 检测到的语言 */}
      {detectedLanguage && (
        <div className="text-sm text-gray-600 text-center">
          检测到的语言: {SUPPORTED_LANGUAGES[detectedLanguage as keyof typeof SUPPORTED_LANGUAGES] || detectedLanguage}
        </div>
      )}

      {/* 翻译结果 */}
      {(isTranslating || translatedText) && (
        <div className="relative mt-4 border rounded-md p-4 bg-gray-50">
          {isTranslating ? (
            <div className="py-10 space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </div>
          ) : (
            <>
              <div className="whitespace-pre-wrap">{translatedText}</div>
              {translatedText && (
                <Button
                  className="absolute bottom-2 right-2"
                  size="sm"
                  variant="ghost"
                  onClick={handleCopyTranslation}
                >
                  复制
                </Button>
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
}
