'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Slider } from '@/components/ui/slider';
import { toast } from 'sonner';
import { useAudioStore, AudioSegment } from '@/store/audioStore';
import { AudioVisualizer } from './AudioVisualizer';
import { VolumeIndicator } from './VolumeIndicator';
import { WaveAnimation } from './WaveAnimation';
import { PulsatingMic } from './PulsatingMic';
import { Separator } from '@/components/ui/separator';

function AudioRecorder() {
  const {
    isRecording,
    audioUrl,
    recordingTime,
    startRecording,
    stopRecording,
    resetRecording,
    segments,
    silenceThreshold,
    silenceDuration,
    maxSilenceDuration,
    maxSegmentDuration,
    setSilenceThreshold,
    setMaxSilenceDuration,
    setMaxSegmentDuration,
    updateSilenceDuration,
    resetSilenceDuration,
    startNewSegment,
    processSegment,
    audioChunks
  } = useAudioStore();

  const [volume, setVolume] = useState<number>(80);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const audioRef = useRef<HTMLAudioElement>(null);

  // 调试状态
  const [currentVolume, setCurrentVolume] = useState<number>(0);
  const [isAnalyzing, setIsAnalyzing] = useState<boolean>(false);
  const [audioChunksCount, setAudioChunksCount] = useState<number>(0);
  const [lastChunkSize, setLastChunkSize] = useState<number>(0);

  // 音频分析相关
  const analyserRef = useRef<AnalyserNode | null>(null);
  const dataArrayRef = useRef<Uint8Array | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const animationFrameRef = useRef<number | null>(null);

  // 格式化录音时间
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 请求麦克风权限
  const requestMicrophonePermission = async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
      setStream(mediaStream);
      return mediaStream;
    } catch (error) {
      toast.error('无法访问麦克风，请检查权限设置');
      console.error('麦克风访问错误:', error);
      return null;
    }
  };

  // 开始录音
  const handleStartRecording = async () => {
    const mediaStream = stream || await requestMicrophonePermission();
    if (mediaStream) {
      startRecording(mediaStream);
      toast.success('开始录音');
    }
  };

  // 停止录音
  const handleStopRecording = async () => {
    await stopRecording();
    toast.success('录音已保存');
  };

  // 重置录音
  const handleResetRecording = () => {
    resetRecording();
    toast.info('录音已重置');
  };

  // 下载录音
  const handleDownload = () => {
    if (audioUrl) {
      const a = document.createElement('a');
      a.href = audioUrl;
      a.download = `recording-${new Date().toISOString()}.wav`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      toast.success('录音已下载');
    }
  };

  // 设置音量
  const handleVolumeChange = (value: number[]) => {
    setVolume(value[0]);
    if (audioRef.current) {
      audioRef.current.volume = value[0] / 100;
    }
  };

  // 初始化音频分析器
  useEffect(() => {
    if (!stream || !isRecording) return;

    console.log('初始化音频分析器');
    setIsAnalyzing(true);

    // 创建音频上下文和分析器
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    const analyser = audioContext.createAnalyser();
    analyser.fftSize = 256;
    const bufferLength = analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    console.log(`音频分析器初始化完成: fftSize=${analyser.fftSize}, bufferLength=${bufferLength}`);

    // 连接麦克风到分析器
    const source = audioContext.createMediaStreamSource(stream);
    source.connect(analyser);

    console.log('麦克风已连接到分析器');

    // 保存引用
    analyserRef.current = analyser;
    dataArrayRef.current = dataArray;
    audioContextRef.current = audioContext;

    // 开始音频分析
    startAudioAnalysis();

    return () => {
      console.log('清理音频分析器资源');
      setIsAnalyzing(false);

      // 清理资源
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }

      if (audioContextRef.current) {
        audioContextRef.current.close();
        audioContextRef.current = null;
      }
    };
  }, [stream, isRecording]);

  // 音频分析和静音检测
  const startAudioAnalysis = () => {
    if (!analyserRef.current || !dataArrayRef.current) {
      console.error('音频分析器未初始化');
      return;
    }

    console.log('开始音频分析');
    const analyser = analyserRef.current;
    const dataArray = dataArrayRef.current;

    // 用于限制日志输出频率
    let logCounter = 0;

    // 用于防止短时间内多次调用handleSegmentEnd
    let lastSegmentEndTime = 0;
    const segmentEndDebounceTime = 1000; // 1秒内不重复触发段落结束

    const analyseAudio = () => {
      // 请求下一帧
      animationFrameRef.current = requestAnimationFrame(analyseAudio);

      // 获取频谱数据
      analyser.getByteFrequencyData(dataArray);

      // 计算平均音量
      let sum = 0;
      for (let i = 0; i < dataArray.length; i++) {
        sum += dataArray[i];
      }
      const averageVolume = sum / dataArray.length / 255; // 归一化到0-1

      // 更新当前音量状态
      setCurrentVolume(averageVolume);

      // 每10帧输出一次日志，避免日志过多
      if (logCounter % 10 === 0) {
        console.log(`当前音量: ${(averageVolume * 100).toFixed(2)}%, 阈值: ${(silenceThreshold * 100).toFixed(2)}%, 静音时间: ${silenceDuration}ms, 音频块数: ${audioChunks.length}`);
      }
      logCounter++;

      // 检测是否有声音
      if (averageVolume > silenceThreshold) {
        // 有声音，重置静音持续时间
        if (silenceDuration > 0) {
          console.log(`检测到声音，重置静音持续时间，当前音量: ${(averageVolume * 100).toFixed(2)}%`);
        }
        resetSilenceDuration();
      } else {
        // 静音，增加静音持续时间
        updateSilenceDuration(100); // 每帧增加100ms

        // 每500ms记录一次静音状态
        if (silenceDuration % 500 === 0 && silenceDuration > 0) {
          console.log(`静音持续中: ${silenceDuration}ms / ${maxSilenceDuration}ms`);
        }

        // 检查静音持续时间是否超过阈值
        if (silenceDuration >= maxSilenceDuration) {
          const now = Date.now();
          // 防抖：确保距离上次段落结束至少有一定时间
          if (now - lastSegmentEndTime > segmentEndDebounceTime) {
            // 静音时间超过阈值，判定为段落结束
            console.log(`静音时间(${silenceDuration}ms)超过阈值(${maxSilenceDuration}ms)，判定为段落结束`);
            handleSegmentEnd();
            lastSegmentEndTime = now;
          } else {
            console.log(`段落结束防抖：上次段落结束时间 ${now - lastSegmentEndTime}ms 前，忽略此次触发`);
          }
        }
      }

      // 检查当前段落是否超过最大时长
      const now = Date.now();
      const currentSegmentDuration = now - useAudioStore.getState().currentSegmentStartTime;

      // 每秒记录一次段落时长
      if (currentSegmentDuration % 1000 < 100) {
        console.log(`当前段落时长: ${(currentSegmentDuration / 1000).toFixed(1)}秒 / ${(maxSegmentDuration / 1000).toFixed(1)}秒`);
      }

      if (currentSegmentDuration > maxSegmentDuration) {
        // 防抖：确保距离上次段落结束至少有一定时间
        if (now - lastSegmentEndTime > segmentEndDebounceTime) {
          // 强制结束当前段落
          console.log(`段落时长(${currentSegmentDuration}ms)超过最大值(${maxSegmentDuration}ms)，强制结束段落`);
          handleSegmentEnd();
          lastSegmentEndTime = now;
        } else {
          console.log(`段落结束防抖：上次段落结束时间 ${now - lastSegmentEndTime}ms 前，忽略此次触发`);
        }
      }

      // 更新音频块计数
      setAudioChunksCount(audioChunks.length);
    };

    // 开始分析
    analyseAudio();
  };

  // 处理段落结束
  const handleSegmentEnd = async () => {
    if (!isRecording) {
      console.log('未在录音状态，忽略段落结束处理');
      return;
    }

    const { mediaRecorder, audioChunks, currentSegmentStartTime } = useAudioStore.getState();

    console.log(`段落结束处理开始，当前状态: mediaRecorder=${mediaRecorder ? mediaRecorder.state : 'null'}, audioChunks=${audioChunks.length}, currentSegmentStartTime=${currentSegmentStartTime}`);

    // 只有当有音频数据且录音器处于活动状态时才处理
    if (audioChunks.length === 0) {
      console.warn('没有音频数据，忽略段落结束处理');
      return;
    }

    if (!mediaRecorder) {
      console.warn('mediaRecorder不存在，忽略段落结束处理');
      return;
    }

    if (mediaRecorder.state !== 'recording') {
      console.warn(`mediaRecorder状态不是recording，当前状态: ${mediaRecorder.state}，忽略段落结束处理`);
      return;
    }

    console.log('检测到段落结束，处理当前段落');

    try {
      // 复制当前音频块数组，而不是暂停录音
      const currentAudioChunks = [...audioChunks];

      // 创建当前段落的音频Blob
      const segmentBlob = new Blob(currentAudioChunks, { type: mediaRecorder.mimeType });
      console.log(`创建段落Blob: 大小=${segmentBlob.size}字节, 类型=${mediaRecorder.mimeType}`);

      // 更新最后块大小
      setLastChunkSize(segmentBlob.size);

      // 计算段落持续时间
      const segmentDuration = Date.now() - currentSegmentStartTime;
      console.log(`段落持续时间: ${segmentDuration}ms`);

      // 开始新的段落（重置音频块和段落开始时间）
      console.log('开始新的段落');
      startNewSegment();

      // 只有当段落时长超过500ms时才处理，避免处理太短的噪音
      if (segmentDuration > 500) {
        try {
          console.log('段落时长足够，开始处理');

          // 转换为WAV格式
          console.log('开始转换为WAV格式');
          const wavBlob = await convertToWav(segmentBlob);
          console.log(`WAV转换完成: 大小=${wavBlob.size}字节`);

          // 处理段落（转录和翻译）
          console.log('开始处理段落（转录和翻译）');
          processSegment(wavBlob, segmentDuration).catch(error => {
            console.error('处理段落异步错误:', error);
          });

          // 注意：我们不等待处理完成，而是立即返回，让MediaRecorder继续录制下一个段落

        } catch (error) {
          console.error('处理段落错误:', error);
          // 继续录音，不需要恢复，因为我们没有暂停
        }
      } else {
        console.log(`段落太短(${segmentDuration}ms < 500ms)，忽略处理`);
      }
    } catch (error) {
      console.error('段落结束处理过程中发生错误:', error);
      // 不需要恢复录音，因为我们没有暂停
    }
  };

  // 从audioStore导入convertToWav函数
  const convertToWav = async (blob: Blob): Promise<Blob> => {
    const { convertToWav } = await import('@/utils/audioUtils');
    return convertToWav(blob, {
      sampleRate: 16000,
      numChannels: 1
    });
  };

  // 组件卸载时清理资源
  useEffect(() => {
    return () => {
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }

      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }

      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, [stream]);

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="text-center flex items-center justify-center gap-2">
          语音录制
          {isRecording && (
            <span className="relative flex h-3 w-3">
              <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
              <span className="relative inline-flex rounded-full h-3 w-3 bg-red-500"></span>
            </span>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 录音时间显示 */}
        <div className="text-center text-2xl font-mono">
          {formatTime(recordingTime)}
        </div>

        {/* 录音进度条 */}
        {isRecording && (
          <Progress value={recordingTime % 60 * (100/60)} className="h-2" />
        )}

        {/* 静音检测设置 */}
        {!isRecording && !audioUrl && (
          <div className="space-y-4 border rounded-md p-3 bg-gray-50">
            <h3 className="text-sm font-medium">自动分段设置</h3>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">静音阈值:</span>
                <span className="text-xs">{(silenceThreshold * 100).toFixed(0)}%</span>
              </div>
              <Slider
                value={[silenceThreshold * 100]}
                min={1}
                max={20}
                step={1}
                onValueChange={(value) => setSilenceThreshold(value[0] / 100)}
              />
              <p className="text-xs text-gray-500">较低的值会使系统对静音更敏感</p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">静音持续时间:</span>
                <span className="text-xs">{(maxSilenceDuration / 1000).toFixed(1)}秒</span>
              </div>
              <Slider
                value={[maxSilenceDuration]}
                min={500}
                max={3000}
                step={100}
                onValueChange={(value) => setMaxSilenceDuration(value[0])}
              />
              <p className="text-xs text-gray-500">静音超过此时长将被视为段落结束</p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">最大段落时长:</span>
                <span className="text-xs">{(maxSegmentDuration / 1000).toFixed(0)}秒</span>
              </div>
              <Slider
                value={[maxSegmentDuration]}
                min={5000}
                max={30000}
                step={1000}
                onValueChange={(value) => setMaxSegmentDuration(value[0])}
              />
              <p className="text-xs text-gray-500">段落超过此时长将被强制分段</p>
            </div>
          </div>
        )}

        {/* 调试信息 */}
        {isRecording && (
          <div className="p-2 border rounded-md bg-gray-50 mb-4">
            <h3 className="text-sm font-medium mb-2">调试信息</h3>
            <div className="space-y-1 text-xs">
              <div className="flex justify-between">
                <span>当前音量:</span>
                <span>{(currentVolume * 100).toFixed(1)}%</span>
              </div>
              <div className="w-full bg-gray-200 h-2 rounded-full overflow-hidden">
                <div
                  className={`h-full transition-all duration-100 ${
                    currentVolume > silenceThreshold ? 'bg-green-500' : 'bg-gray-400'
                  }`}
                  style={{ width: `${currentVolume * 100}%` }}
                />
              </div>
              <div className="flex justify-between">
                <span>静音阈值:</span>
                <span>{(silenceThreshold * 100).toFixed(1)}%</span>
              </div>
              <div className="flex justify-between">
                <span>静音持续时间:</span>
                <span>{silenceDuration}ms / {maxSilenceDuration}ms</span>
              </div>
              <div className="w-full bg-gray-200 h-1 rounded-full overflow-hidden">
                <div
                  className="h-full bg-yellow-500 transition-all duration-100"
                  style={{ width: `${(silenceDuration / maxSilenceDuration) * 100}%` }}
                />
              </div>
              <div className="flex justify-between">
                <span>音频块数:</span>
                <span>{audioChunksCount}</span>
              </div>
              <div className="flex justify-between">
                <span>最后块大小:</span>
                <span>{lastChunkSize} 字节</span>
              </div>
              <div className="flex justify-between">
                <span>分析状态:</span>
                <span>{isAnalyzing ? '运行中' : '未运行'}</span>
              </div>
            </div>
          </div>
        )}

        {/* 音频可视化 */}
        {isRecording && (
          <div className="space-y-2">
            <PulsatingMic isRecording={isRecording} />
            <AudioVisualizer stream={stream} isRecording={isRecording} />
            <VolumeIndicator stream={stream} isRecording={isRecording} />
            <WaveAnimation isRecording={isRecording} />
          </div>
        )}

        {/* 段落列表 */}
        {segments.length > 0 && (
          <div className="mt-4">
            <h3 className="text-sm font-medium mb-2">识别段落 ({segments.length})</h3>
            <div className="space-y-3 max-h-60 overflow-y-auto p-2 border rounded-md">
              {segments.map((segment) => (
                <div key={segment.id} className="p-2 bg-gray-50 rounded border">
                  <div className="flex justify-between text-xs text-gray-500 mb-1">
                    <span>时长: {(segment.duration / 1000).toFixed(1)}秒</span>
                    <span>{new Date(segment.timestamp).toLocaleTimeString()}</span>
                  </div>

                  {segment.isProcessing ? (
                    <div className="text-sm text-center py-2">处理中...</div>
                  ) : segment.error ? (
                    <div className="text-sm text-red-500">{segment.error}</div>
                  ) : (
                    <>
                      {segment.transcription && (
                        <div className="mb-2">
                          <div className="text-xs text-gray-500 mb-1">识别结果:</div>
                          <p className="text-sm">{segment.transcription}</p>
                        </div>
                      )}

                      {segment.translation && (
                        <>
                          <Separator className="my-2" />
                          <div>
                            <div className="text-xs text-gray-500 mb-1">翻译结果:</div>
                            <p className="text-sm text-blue-600">{segment.translation}</p>
                          </div>
                        </>
                      )}
                    </>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 音频播放器 */}
        {audioUrl && (
          <div className="space-y-2 mt-4">
            <audio ref={audioRef} src={audioUrl} controls className="w-full" />
            <div className="flex items-center space-x-2">
              <span className="text-sm">音量:</span>
              <Slider
                value={[volume]}
                min={0}
                max={100}
                step={1}
                onValueChange={handleVolumeChange}
              />
              <span className="text-sm w-8">{volume}%</span>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-center space-x-2">
        {!isRecording && !audioUrl && (
          <Button
            onClick={handleStartRecording}
            className="relative overflow-hidden group"
          >
            <span className="relative z-10">开始录音</span>
            <span className="absolute inset-0 bg-white/20 transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left duration-300"></span>
          </Button>
        )}

        {isRecording && (
          <Button
            variant="destructive"
            onClick={handleStopRecording}
            className="relative overflow-hidden group animate-pulse"
          >
            <span className="relative z-10 flex items-center gap-2">
              <span className="inline-block w-3 h-3 bg-white rounded-sm"></span>
              停止录音
            </span>
          </Button>
        )}

        {audioUrl && (
          <>
            <Button
              variant="outline"
              onClick={handleResetRecording}
              className="hover:scale-105 transition-transform"
            >
              <span className="flex items-center gap-1">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="animate-spin-slow">
                  <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"/>
                  <path d="M3 3v5h5"/>
                </svg>
                重置
              </span>
            </Button>
            <Button
              variant="default"
              onClick={handleStartRecording}
              className="hover:bg-opacity-90 transition-all"
            >
              <span className="flex items-center gap-1">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="10"/>
                  <circle cx="12" cy="12" r="3"/>
                </svg>
                重新录制
              </span>
            </Button>
            <Button
              variant="secondary"
              onClick={handleDownload}
              className="hover:shadow-md transition-shadow"
            >
              <span className="flex items-center gap-1">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                  <polyline points="7 10 12 15 17 10"/>
                  <line x1="12" y1="15" x2="12" y2="3"/>
                </svg>
                下载
              </span>
            </Button>
          </>
        )}
      </CardFooter>
    </Card>
  );
}

export { AudioRecorder };
