'use client';

interface PulsatingMicProps {
  isRecording: boolean;
}

export function PulsatingMic({ isRecording }: PulsatingMicProps) {
  if (!isRecording) return null;
  
  return (
    <div className="flex justify-center my-4">
      <div className="relative">
        {/* 脉动圆圈 */}
        <div className="absolute -inset-4 rounded-full bg-red-500/20 animate-ping" />
        <div className="absolute -inset-3 rounded-full bg-red-500/30 animate-ping animation-delay-300" />
        <div className="absolute -inset-2 rounded-full bg-red-500/40 animate-ping animation-delay-600" />
        
        {/* 麦克风图标 */}
        <div className="relative z-10 bg-red-500 text-white p-3 rounded-full">
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            width="24" 
            height="24" 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor" 
            strokeWidth="2" 
            strokeLinecap="round" 
            strokeLinejoin="round"
          >
            <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z" />
            <path d="M19 10v2a7 7 0 0 1-14 0v-2" />
            <line x1="12" x2="12" y1="19" y2="22" />
          </svg>
        </div>
      </div>
    </div>
  );
}
