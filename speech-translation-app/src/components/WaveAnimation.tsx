'use client';

interface WaveAnimationProps {
  isRecording: boolean;
}

export function WaveAnimation({ isRecording }: WaveAnimationProps) {
  if (!isRecording) return null;
  
  // 创建一个波形条的数组
  const bars = Array.from({ length: 20 }, (_, i) => i);
  
  return (
    <div className="flex items-center justify-center h-16 my-4">
      <div className="flex items-end space-x-1">
        {bars.map((i) => {
          // 为每个条设置随机的高度和动画延迟
          const height = Math.floor(Math.random() * 100) % 60 + 20;
          const delay = Math.random() * 1;
          
          return (
            <div
              key={i}
              className="bg-primary w-1 rounded-t-sm animate-wave"
              style={{
                height: `${height}%`,
                animationDelay: `${delay}s`,
                animationDuration: `${0.5 + Math.random() * 0.5}s`,
              }}
            />
          );
        })}
      </div>
    </div>
  );
}
