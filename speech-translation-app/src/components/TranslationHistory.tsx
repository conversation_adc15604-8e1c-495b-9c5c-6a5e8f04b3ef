/**
 * 翻译历史记录组件
 */

'use client';

import React, { useState } from 'react';
import { useTranslationStore, SUPPORTED_LANGUAGES } from '@/store/translationStore';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';

/**
 * 翻译历史记录组件
 */
export default function TranslationHistory() {
  // 使用翻译状态
  const { history, clearHistory, setSourceText, setSourceLanguage, setTargetLanguage } = useTranslationStore();

  // 对话框状态
  const [open, setOpen] = useState(false);

  // 格式化时间戳
  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  // 处理点击历史记录项
  const handleHistoryItemClick = (item: typeof history[0]) => {
    setSourceText(item.sourceText);
    setSourceLanguage(item.sourceLanguage);
    setTargetLanguage(item.targetLanguage);
    setOpen(false);
  };

  // 如果没有历史记录，不显示按钮
  if (history.length === 0) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {/* 历史记录按钮 */}
      <DialogTrigger asChild>
        <Button
          className="fixed bottom-5 right-5 z-10"
          variant="outline"
          size="sm"
        >
          历史记录
        </Button>
      </DialogTrigger>

      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>翻译历史记录</DialogTitle>
          <DialogDescription>
            您最近的翻译历史，点击任意记录可重新编辑。
          </DialogDescription>
        </DialogHeader>

        <div className="max-h-[60vh] overflow-y-auto py-2">
          {history.length === 0 ? (
            <p className="text-center text-gray-500 py-4">
              暂无翻译历史记录
            </p>
          ) : (
            <div className="space-y-4">
              {history.map((item) => (
                <div
                  key={item.id}
                  className="p-3 border rounded-md cursor-pointer hover:bg-gray-50"
                  onClick={() => handleHistoryItemClick(item)}
                >
                  <div className="flex justify-between mb-2 text-sm">
                    <span className="text-gray-500">
                      {formatTimestamp(item.timestamp)}
                    </span>
                    <span className="text-blue-500">
                      {SUPPORTED_LANGUAGES[item.sourceLanguage as keyof typeof SUPPORTED_LANGUAGES] || item.sourceLanguage} → {SUPPORTED_LANGUAGES[item.targetLanguage as keyof typeof SUPPORTED_LANGUAGES] || item.targetLanguage}
                    </span>
                  </div>

                  <p className="font-medium line-clamp-2">
                    {item.sourceText}
                  </p>

                  <Separator className="my-2" />

                  <p className="text-gray-700 line-clamp-2">
                    {item.translatedText}
                  </p>
                </div>
              ))}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="destructive"
            onClick={clearHistory}
            size="sm"
          >
            清空历史记录
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
