/**
 * 语音识别结果翻译组件
 */

'use client';

import React, { useEffect, useState } from 'react';
import { useTranslationStore, SUPPORTED_LANGUAGES } from '@/store/translationStore';
import { useSpeechRecognitionStore } from '@/store/speechRecognitionStore';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';

/**
 * 语音识别结果翻译组件
 */
export default function SpeechTranslationResult() {
  // 使用语音识别状态
  const { recognitionResult, isRecognizing } = useSpeechRecognitionStore();

  // 使用翻译状态
  const {
    isTranslating,
    translatedText,
    targetLanguage,
    setTargetLanguage,
    setSourceText,
    translate,
    error,
  } = useTranslationStore();

  // 本地状态
  const [autoTranslate, setAutoTranslate] = useState(true);
  const [copied, setCopied] = useState(false);

  // 当识别结果变化时，自动翻译
  useEffect(() => {
    if (recognitionResult && autoTranslate && !isRecognizing) {
      setSourceText(recognitionResult);
      translate();
    }
  }, [recognitionResult, autoTranslate, isRecognizing]);

  // 手动翻译
  const handleTranslate = () => {
    if (recognitionResult) {
      setSourceText(recognitionResult);
      translate();
    }
  };

  // 复制翻译结果
  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    toast.success("已复制到剪贴板");
    setTimeout(() => setCopied(false), 2000);
  };

  // 处理目标语言变化
  const handleTargetLanguageChange = (value: string) => {
    setTargetLanguage(value);
    // 如果已经有识别结果，自动重新翻译
    if (recognitionResult && autoTranslate) {
      setTimeout(() => {
        setSourceText(recognitionResult);
        translate();
      }, 100);
    }
  };

  // 处理自动翻译切换
  const handleAutoTranslateChange = () => {
    setAutoTranslate(!autoTranslate);
  };

  return (
    <Card className="w-full max-w-4xl mx-auto mt-6">
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <span>识别与翻译结果</span>

          <div className="flex items-center space-x-4 text-sm font-normal">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="autoTranslate"
                checked={autoTranslate}
                onChange={handleAutoTranslateChange}
                className="mr-2"
              />
              <label htmlFor="autoTranslate">自动翻译</label>
            </div>

            <div className="flex items-center">
              <span className="mr-2">翻译为:</span>
              <Select value={targetLanguage} onValueChange={handleTargetLanguageChange}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="选择语言" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(SUPPORTED_LANGUAGES).map(([code, name]) => (
                    <SelectItem key={code} value={code}>
                      {name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 识别结果 */}
        <div className="p-3 bg-gray-50 rounded-md min-h-[80px] relative">
          <h3 className="text-sm font-medium mb-2">语音识别结果:</h3>

          {isRecognizing ? (
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </div>
          ) : recognitionResult ? (
            <>
              <p className="whitespace-pre-wrap">{recognitionResult}</p>
              <Button
                variant="ghost"
                size="sm"
                className="absolute bottom-2 right-2"
                onClick={() => handleCopy(recognitionResult)}
              >
                复制
              </Button>
            </>
          ) : (
            <p className="text-gray-400 text-center">暂无识别结果</p>
          )}
        </div>

        {/* 翻译结果 */}
        <div className="p-3 bg-blue-50 rounded-md min-h-[80px] relative">
          <h3 className="text-sm font-medium mb-2">翻译结果 ({SUPPORTED_LANGUAGES[targetLanguage as keyof typeof SUPPORTED_LANGUAGES]}):</h3>

          {isTranslating ? (
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </div>
          ) : translatedText ? (
            <>
              <p className="whitespace-pre-wrap">{translatedText}</p>
              <Button
                variant="ghost"
                size="sm"
                className="absolute bottom-2 right-2"
                onClick={() => handleCopy(translatedText)}
              >
                复制
              </Button>
            </>
          ) : error ? (
            <p className="text-red-500 text-center">{error}</p>
          ) : (
            <p className="text-gray-400 text-center">暂无翻译结果</p>
          )}
        </div>
      </CardContent>

      {/* 手动翻译按钮 */}
      {!autoTranslate && recognitionResult && (
        <CardFooter className="flex justify-center">
          <Button
            onClick={handleTranslate}
            disabled={isTranslating}
            className="bg-blue-500 hover:bg-blue-600 text-white"
          >
            {isTranslating ? '翻译中...' : '翻译'}
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}
