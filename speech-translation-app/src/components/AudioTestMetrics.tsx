'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface TestResult {
  id: number;
  timestamp: string;
  audioSize: number; // 字节
  duration: number; // 秒
  uploadTime: number; // 毫秒
  success: boolean;
  error?: string;
}

export function AudioTestMetrics() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);

  // 从localStorage加载测试结果
  useEffect(() => {
    const savedResults = localStorage.getItem('audioTestResults');
    if (savedResults) {
      try {
        setTestResults(JSON.parse(savedResults));
      } catch (error) {
        console.error('Failed to parse saved test results:', error);
      }
    }
  }, []);

  // 保存测试结果到localStorage
  useEffect(() => {
    if (testResults.length > 0) {
      localStorage.setItem('audioTestResults', JSON.stringify(testResults));
    }
  }, [testResults]);

  // 添加测试结果
  const addTestResult = (result: Omit<TestResult, 'id' | 'timestamp'>) => {
    const newResult: TestResult = {
      ...result,
      id: Date.now(),
      timestamp: new Date().toISOString(),
    };

    setTestResults(prev => [...prev, newResult]);
  };

  // 清除测试结果
  const clearTestResults = () => {
    setTestResults([]);
    localStorage.removeItem('audioTestResults');
  };

  // 计算平均上传速度 (MB/s)
  const calculateAverageSpeed = () => {
    if (testResults.length === 0) return 0;

    const successfulTests = testResults.filter(r => r.success);
    if (successfulTests.length === 0) return 0;

    const totalSize = successfulTests.reduce((sum, r) => sum + r.audioSize, 0);
    const totalTime = successfulTests.reduce((sum, r) => sum + r.uploadTime, 0);

    // 转换为MB/s
    return (totalSize / 1024 / 1024) / (totalTime / 1000);
  };

  // 计算成功率
  const calculateSuccessRate = () => {
    if (testResults.length === 0) return 0;

    const successfulTests = testResults.filter(r => r.success);
    return (successfulTests.length / testResults.length) * 100;
  };

  // 监听上传完成事件
  useEffect(() => {
    const handleUploadComplete = (event: CustomEvent) => {
      const { audioSize, duration, uploadTime, success, error } = event.detail;
      addTestResult({ audioSize, duration, uploadTime, success, error });
    };

    window.addEventListener('audioUploadComplete' as any, handleUploadComplete);

    return () => {
      window.removeEventListener('audioUploadComplete' as any, handleUploadComplete);
    };
  }, []);

  // 如果没有测试结果，不显示组件
  if (testResults.length === 0) return null;

  return (
    <Card className="w-full max-w-md mx-auto mt-6">
      <CardHeader>
        <CardTitle className="text-center">音频上传测试指标</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-100 p-3 rounded-md">
              <p className="text-sm text-gray-500">测试次数</p>
              <p className="text-xl font-bold">{testResults.length}</p>
            </div>
            <div className="bg-gray-100 p-3 rounded-md">
              <p className="text-sm text-gray-500">成功率</p>
              <p className="text-xl font-bold">{calculateSuccessRate().toFixed(1)}%</p>
            </div>
            <div className="bg-gray-100 p-3 rounded-md">
              <p className="text-sm text-gray-500">平均上传速度</p>
              <p className="text-xl font-bold">{calculateAverageSpeed().toFixed(2)} MB/s</p>
            </div>
            <div className="bg-gray-100 p-3 rounded-md">
              <p className="text-sm text-gray-500">平均文件大小</p>
              <p className="text-xl font-bold">
                {(testResults.reduce((sum, r) => sum + r.audioSize, 0) / testResults.length / 1024 / 1024).toFixed(2)} MB
              </p>
            </div>
          </div>

          <div className="mt-4">
            <h3 className="font-medium mb-2">最近测试结果</h3>
            <div className="max-h-40 overflow-y-auto space-y-2">
              {testResults.slice().reverse().slice(0, 5).map(result => (
                <div
                  key={result.id}
                  className={`text-xs p-2 rounded ${result.success ? 'bg-green-50' : 'bg-red-50'}`}
                >
                  <p>
                    <span className="font-medium">
                      {new Date(result.timestamp).toLocaleString()}
                    </span> -
                    {result.success ? ' 成功' : ' 失败'}
                  </p>
                  <p>大小: {(result.audioSize / 1024 / 1024).toFixed(2)} MB,
                     时长: {result.duration.toFixed(1)}s,
                     上传耗时: {
                       result.uploadTime < 1000
                         ? `${result.uploadTime} 毫秒`
                         : `${(result.uploadTime / 1000).toFixed(2)} 秒`
                     }</p>
                  {result.error && <p className="text-red-500">{result.error}</p>}
                </div>
              ))}
            </div>
          </div>

          <div className="flex justify-end mt-4">
            <Button variant="outline" size="sm" onClick={clearTestResults}>
              清除测试数据
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
