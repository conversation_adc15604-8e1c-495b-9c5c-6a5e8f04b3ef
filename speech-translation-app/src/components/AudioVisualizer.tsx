'use client';

import { useEffect, useRef } from 'react';

interface AudioVisualizerProps {
  stream: MediaStream | null;
  isRecording: boolean;
}

export function AudioVisualizer({ stream, isRecording }: AudioVisualizerProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number | undefined>(undefined);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const dataArrayRef = useRef<Uint8Array | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);

  // 初始化音频分析器
  useEffect(() => {
    if (!stream || !isRecording) return;

    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    const analyser = audioContext.createAnalyser();
    analyser.fftSize = 256;
    const bufferLength = analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    const source = audioContext.createMediaStreamSource(stream);
    source.connect(analyser);
    // 不连接到目标节点，避免回声
    // analyser.connect(audioContext.destination);

    analyserRef.current = analyser;
    dataArrayRef.current = dataArray;
    audioContextRef.current = audioContext;

    return () => {
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, [stream, isRecording]);

  // 绘制音频波形
  useEffect(() => {
    if (!isRecording || !analyserRef.current || !dataArrayRef.current) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const canvasCtx = canvas.getContext('2d');
    if (!canvasCtx) return;

    const analyser = analyserRef.current;
    const dataArray = dataArrayRef.current;

    const draw = () => {
      // 请求下一帧动画
      animationRef.current = requestAnimationFrame(draw);

      // 获取音频数据
      analyser.getByteFrequencyData(dataArray);

      // 清空画布
      canvasCtx.clearRect(0, 0, canvas.width, canvas.height);

      // 设置波形样式
      canvasCtx.fillStyle = 'rgb(200, 200, 200)';
      canvasCtx.fillRect(0, 0, canvas.width, canvas.height);

      const barWidth = (canvas.width / dataArray.length) * 2.5;
      let barHeight;
      let x = 0;

      // 绘制波形
      for (let i = 0; i < dataArray.length; i++) {
        barHeight = dataArray[i] / 2;

        // 根据音量大小渐变颜色
        const r = 100 + (barHeight * 155) / 100;
        const g = 50 + (barHeight * 205) / 100;
        const b = 255;

        canvasCtx.fillStyle = `rgb(${r}, ${g}, ${b})`;
        canvasCtx.fillRect(x, canvas.height - barHeight, barWidth, barHeight);

        x += barWidth + 1;
      }
    };

    draw();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isRecording]);

  return (
    <div className="w-full h-24 bg-gray-100 dark:bg-gray-800 rounded-md overflow-hidden">
      <canvas
        ref={canvasRef}
        className="w-full h-full"
        width={300}
        height={100}
      />
    </div>
  );
}
