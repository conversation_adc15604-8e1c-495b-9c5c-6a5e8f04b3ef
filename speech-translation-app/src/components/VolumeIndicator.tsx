'use client';

import { useEffect, useRef, useState } from 'react';

interface VolumeIndicatorProps {
  stream: MediaStream | null;
  isRecording: boolean;
}

export function VolumeIndicator({ stream, isRecording }: VolumeIndicatorProps) {
  const [volume, setVolume] = useState(0);
  const animationRef = useRef<number | undefined>(undefined);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const dataArrayRef = useRef<Uint8Array | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);

  // 初始化音频分析器
  useEffect(() => {
    if (!stream || !isRecording) {
      setVolume(0);
      return;
    }

    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    const analyser = audioContext.createAnalyser();
    analyser.fftSize = 32;
    const bufferLength = analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    const source = audioContext.createMediaStreamSource(stream);
    source.connect(analyser);

    analyserRef.current = analyser;
    dataArrayRef.current = dataArray;
    audioContextRef.current = audioContext;

    return () => {
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, [stream, isRecording]);

  // 更新音量指示器
  useEffect(() => {
    if (!isRecording || !analyserRef.current || !dataArrayRef.current) {
      setVolume(0);
      return;
    }

    const analyser = analyserRef.current;
    const dataArray = dataArrayRef.current;

    const updateVolume = () => {
      animationRef.current = requestAnimationFrame(updateVolume);

      analyser.getByteFrequencyData(dataArray);

      // 计算平均音量
      let sum = 0;
      for (let i = 0; i < dataArray.length; i++) {
        sum += dataArray[i];
      }
      const avg = sum / dataArray.length;

      // 将音量标准化到0-100
      const normalizedVolume = Math.min(100, Math.max(0, avg * 100 / 255));
      setVolume(normalizedVolume);
    };

    updateVolume();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isRecording]);

  // 根据音量计算颜色
  const getColor = () => {
    if (volume < 30) return 'bg-green-500';
    if (volume < 70) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="w-full mt-2">
      <div className="flex items-center gap-2">
        <div className="text-xs w-10">
          {Math.round(volume)}%
        </div>
        <div className="flex-1 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
          <div
            className={`h-full ${getColor()} transition-all duration-100`}
            style={{ width: `${volume}%` }}
          />
        </div>
        <div className="w-5">
          {volume > 0 && (
            <div className={`w-3 h-3 rounded-full ${getColor()} animate-pulse`} />
          )}
        </div>
      </div>
    </div>
  );
}
