'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { useSpeechRecognitionStore } from '@/store/speechRecognitionStore';

// 将情绪标签转换为中文
function getEmotionLabel(emotionTag: string): string {
  switch (emotionTag.toLowerCase()) {
    case 'positive':
      return '正面情绪';
    case 'negative':
      return '负面情绪';
    case 'neutral':
      return '中性情绪';
    default:
      return emotionTag;
  }
}

interface SpeechRecognitionResultProps {
  audioBlob: Blob | null;
  onReset: () => void;
}

export function SpeechRecognitionResult({ audioBlob, onReset }: SpeechRecognitionResultProps) {
  // 使用语音识别状态存储
  const {
    isRecognizing,
    recognitionResult: storedR<PERSON><PERSON>,
    setR<PERSON>ognizing,
    setRecognitionResult,
    setError
  } = useSpeechRecognitionStore();

  // 本地状态用于存储识别的元数据
  const [recognitionMetadata, setRecognitionMetadata] = useState<{
    confidence?: number;
    duration?: number;
    emotionTag?: string;
    emotionConfidence?: number;
  } | null>(null);

  // 开始语音识别
  const handleRecognize = async () => {
    if (!audioBlob) {
      toast.error('没有可识别的音频，请先录制音频');
      return;
    }

    setRecognizing(true);
    setRecognitionResult('');
    setRecognitionMetadata(null);

    try {
      // 创建FormData
      const formData = new FormData();
      formData.append('audio', audioBlob, 'recording.wav');
      formData.append('format', 'wav');
      formData.append('sampleRate', '16000');

      // 发送请求
      const response = await fetch('/api/speech', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        // 更新识别结果文本
        setRecognitionResult(result.text);

        // 更新元数据
        setRecognitionMetadata({
          confidence: result.confidence,
          duration: result.duration,
          emotionTag: result.emotionTag,
          emotionConfidence: result.emotionConfidence,
        });

        toast.success('语音识别成功');
      } else {
        setError(`识别失败: ${result.message}`);
        toast.error(`识别失败: ${result.message}`);
      }
    } catch (error) {
      console.error('识别错误:', error);
      setError('识别过程中发生错误');
      toast.error('识别过程中发生错误');
    } finally {
      setRecognizing(false);
    }
  };

  // 复制文本
  const handleCopyText = () => {
    if (storedResult) {
      navigator.clipboard.writeText(storedResult)
        .then(() => toast.success('文本已复制到剪贴板'))
        .catch(() => toast.error('复制失败'));
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto mt-6">
      <CardHeader>
        <CardTitle className="text-center">语音识别结果</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {isRecognizing ? (
          <div className="space-y-2">
            <p className="text-center text-sm">正在识别中...</p>
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </div>
          </div>
        ) : storedResult ? (
          <div className="space-y-3">
            <div className="p-3 bg-gray-50 rounded-md">
              <p className="whitespace-pre-wrap">{storedResult}</p>
            </div>

            {recognitionMetadata?.confidence !== undefined && (
              <p className="text-sm text-gray-500">
                置信度: {(recognitionMetadata.confidence * 100).toFixed(1)}%
              </p>
            )}

            {recognitionMetadata?.duration !== undefined && (
              <p className="text-sm text-gray-500">
                处理时间: {recognitionMetadata.duration} 毫秒
              </p>
            )}

            {recognitionMetadata?.emotionTag && (
              <p className="text-sm text-gray-500">
                情绪分析: {getEmotionLabel(recognitionMetadata.emotionTag)}
                {recognitionMetadata.emotionConfidence !== undefined &&
                  ` (置信度: ${(recognitionMetadata.emotionConfidence * 100).toFixed(1)}%)`}
              </p>
            )}
          </div>
        ) : (
          <p className="text-center text-sm text-gray-500">
            点击下方按钮开始识别录制的音频
          </p>
        )}
      </CardContent>
      <CardFooter className="flex justify-center space-x-2">
        {!isRecognizing && !storedResult && audioBlob && (
          <Button onClick={handleRecognize} disabled={isRecognizing}>
            开始识别
          </Button>
        )}

        {storedResult && (
          <>
            <Button variant="outline" onClick={handleCopyText}>
              复制文本
            </Button>
            <Button variant="outline" onClick={onReset}>
              重新录制
            </Button>
            <Button variant="default" onClick={handleRecognize}>
              重新识别
            </Button>
          </>
        )}
      </CardFooter>
    </Card>
  );
}
