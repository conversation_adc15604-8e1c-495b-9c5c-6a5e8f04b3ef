import { create } from 'zustand';
import { convertToWav } from '@/utils/audioUtils';
import { useTranslationStore } from './translationStore';

// 音频段落接口
export interface AudioSegment {
  id: string;
  audioBlob: Blob;
  timestamp: number;
  duration: number;
  isProcessing: boolean;
  transcription: string | null;
  translation: string | null;
  error: string | null;
}

export interface AudioState {
  isRecording: boolean;
  audioBlob: Blob | null;
  audioUrl: string | null;
  mediaRecorder: MediaRecorder | null;
  audioChunks: Blob[];
  recordingTime: number;

  // 段落管理
  segments: AudioSegment[];
  currentSegmentStartTime: number;
  isProcessingSegment: boolean;

  // 静音检测配置
  silenceThreshold: number;
  silenceDuration: number;
  maxSilenceDuration: number;
  maxSegmentDuration: number;

  // 操作方法
  startRecording: (stream: MediaStream) => void;
  stopRecording: () => Promise<void>;
  resetRecording: () => void;
  updateRecordingTime: () => void;

  // 段落处理方法
  processSegment: (audioBlob: Blob, duration: number) => Promise<void>;
  setSilenceThreshold: (threshold: number) => void;
  setMaxSilenceDuration: (duration: number) => void;
  setMaxSegmentDuration: (duration: number) => void;
  updateSilenceDuration: (increment: number) => void;
  resetSilenceDuration: () => void;
  startNewSegment: () => void;
}

export const useAudioStore = create<AudioState>((set, get) => ({
  isRecording: false,
  audioBlob: null,
  audioUrl: null,
  mediaRecorder: null,
  audioChunks: [],
  recordingTime: 0,

  // 段落管理
  segments: [],
  currentSegmentStartTime: 0,
  isProcessingSegment: false,

  // 静音检测配置
  silenceThreshold: 0.05, // 静音阈值(0-1)
  silenceDuration: 0,     // 当前静音持续时间(ms)
  maxSilenceDuration: 1500, // 判定为段落结束的静音时长(ms)
  maxSegmentDuration: 10000, // 最大段落时长(ms)

  startRecording: (stream: MediaStream) => {
    // 尝试使用更适合的录音参数
    let options = {};

    // 检查支持的MIME类型
    const supportedTypes = [
      'audio/webm;codecs=pcm',
      'audio/webm',
      'audio/wav',
      'audio/ogg;codecs=opus',
      'audio/mp4'
    ];

    for (const type of supportedTypes) {
      if (MediaRecorder.isTypeSupported(type)) {
        options = { mimeType: type };
        console.log('使用录音MIME类型:', type);
        break;
      }
    }

    const mediaRecorder = new MediaRecorder(stream, options);
    const audioChunks: Blob[] = [];

    mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        console.log(`收集到音频块: ${event.data.size} 字节, 当前状态: ${mediaRecorder.state}`);
        audioChunks.push(event.data);
        set({ audioChunks });
      } else {
        console.log(`收集到空音频块, 当前状态: ${mediaRecorder.state}`);
      }
    };

    // 添加其他事件监听器用于调试
    mediaRecorder.onpause = () => {
      console.log('MediaRecorder暂停');
    };

    mediaRecorder.onresume = () => {
      console.log('MediaRecorder恢复');
    };

    mediaRecorder.onstop = () => {
      console.log('MediaRecorder停止');
    };

    mediaRecorder.onerror = (event) => {
      console.error('MediaRecorder错误:', event);
    };

    // 每100毫秒收集一次数据，提高音频质量
    mediaRecorder.start(100);

    console.log('开始录音，使用MIME类型:', mediaRecorder.mimeType);

    // 重置段落相关状态
    set({
      isRecording: true,
      mediaRecorder,
      audioChunks,
      recordingTime: 0,
      segments: [],
      currentSegmentStartTime: Date.now(),
      silenceDuration: 0,
    });

    // 开始计时
    const intervalId = setInterval(() => {
      get().updateRecordingTime();
    }, 1000);

    // 保存intervalId以便后续清除
    (mediaRecorder as any).timeInterval = intervalId;
  },

  stopRecording: async () => {
    const { mediaRecorder, audioChunks, currentSegmentStartTime, isRecording } = get();

    // 如果已经不在录音状态，直接返回
    if (!isRecording) {
      console.log('已经不在录音状态，忽略停止录音请求');
      return;
    }

    // 如果mediaRecorder不存在，直接设置状态为非录音状态
    if (!mediaRecorder) {
      console.warn('mediaRecorder不存在，直接设置为非录音状态');
      set({ isRecording: false });
      return;
    }

    console.log(`停止录音，当前状态: mediaRecorder=${mediaRecorder.state}, audioChunks=${audioChunks.length}`);

    // 立即设置isRecording为false，防止重复点击
    set({ isRecording: false });

    // 清除计时器
    if ((mediaRecorder as any).timeInterval) {
      clearInterval((mediaRecorder as any).timeInterval);
    }

    return new Promise<void>((resolve, reject) => {
      // 设置超时，确保即使onstop事件没有触发，Promise也会resolve
      const timeoutId = setTimeout(() => {
        console.warn('停止录音超时，强制完成');
        finishRecording();
      }, 3000); // 3秒超时

      // 处理完成录音的函数
      const finishRecording = async () => {
        clearTimeout(timeoutId);

        try {
          // 首先创建原始Blob
          const rawBlob = new Blob(audioChunks, { type: mediaRecorder.mimeType });

          console.log('开始转换音频格式为WAV...');
          console.log('原始音频MIME类型:', mediaRecorder.mimeType);
          console.log('原始音频大小:', rawBlob.size, '字节');

          // 转换为WAV格式（单声道，16000Hz采样率）
          const audioBlob = await convertToWav(rawBlob, {
            sampleRate: 16000,
            numChannels: 1
          });

          console.log('转换后WAV音频大小:', audioBlob.size, '字节');
          const audioUrl = URL.createObjectURL(audioBlob);

          // 如果当前有未处理的段落，处理最后一个段落
          if (audioChunks.length > 0) {
            const segmentDuration = Date.now() - currentSegmentStartTime;
            // 只有当段落时长超过500ms时才处理，避免处理太短的噪音
            if (segmentDuration > 500) {
              try {
                await get().processSegment(audioBlob, segmentDuration);
              } catch (error) {
                console.error('处理最后一个段落失败:', error);
              }
            }
          }

          set({
            audioBlob,
            audioUrl,
            mediaRecorder: null,
          });

          resolve();
        } catch (error) {
          console.error('音频格式转换失败:', error);
          // 如果转换失败，使用原始Blob
          try {
            const rawBlob = new Blob(audioChunks, { type: mediaRecorder.mimeType });
            const audioUrl = URL.createObjectURL(rawBlob);

            set({
              audioBlob: rawBlob,
              audioUrl,
              mediaRecorder: null,
            });

            resolve();
          } catch (blobError) {
            console.error('创建Blob失败:', blobError);
            set({
              mediaRecorder: null,
            });
            reject(blobError);
          }
        }
      };

      // 设置onstop事件处理程序
      mediaRecorder.onstop = finishRecording;

      // 尝试停止录音
      try {
        if (mediaRecorder.state === 'recording' || mediaRecorder.state === 'paused') {
          console.log(`尝试停止录音，当前状态: ${mediaRecorder.state}`);
          mediaRecorder.stop();
        } else {
          console.warn(`mediaRecorder状态不是recording或paused，当前状态: ${mediaRecorder.state}，直接完成录音`);
          finishRecording();
        }
      } catch (error) {
        console.error('停止录音失败:', error);
        finishRecording();
      }
    });
  },

  resetRecording: () => {
    const { audioUrl } = get();

    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
    }

    set({
      audioBlob: null,
      audioUrl: null,
      audioChunks: [],
      recordingTime: 0,
      segments: [],
      silenceDuration: 0,
    });
  },

  updateRecordingTime: () => {
    set((state) => ({
      recordingTime: state.recordingTime + 1,
    }));
  },

  // 更新静音持续时间
  updateSilenceDuration: (increment: number) => {
    set((state) => ({
      silenceDuration: state.silenceDuration + increment
    }));
  },

  // 重置静音持续时间
  resetSilenceDuration: () => {
    set({ silenceDuration: 0 });
  },

  // 设置静音阈值
  setSilenceThreshold: (threshold: number) => {
    set({ silenceThreshold: threshold });
  },

  // 设置最大静音持续时间
  setMaxSilenceDuration: (duration: number) => {
    set({ maxSilenceDuration: duration });
  },

  // 设置最大段落时长
  setMaxSegmentDuration: (duration: number) => {
    set({ maxSegmentDuration: duration });
  },

  // 开始新的段落
  startNewSegment: () => {
    // 保存当前时间作为新段落的开始时间
    const newSegmentStartTime = Date.now();

    console.log(`开始新的段落，时间戳: ${newSegmentStartTime}`);

    set({
      currentSegmentStartTime: newSegmentStartTime,
      audioChunks: [], // 清空音频块数组
      silenceDuration: 0 // 重置静音持续时间
    });
  },

  // 处理音频段落
  processSegment: async (audioBlob: Blob, duration: number) => {
    try {
      // 生成唯一ID
      const segmentId = `segment-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

      // 创建新的段落对象
      const newSegment: AudioSegment = {
        id: segmentId,
        audioBlob,
        timestamp: Date.now(),
        duration,
        isProcessing: true,
        transcription: null,
        translation: null,
        error: null
      };

      // 添加到段落列表
      set((state) => ({
        segments: [...state.segments, newSegment],
        isProcessingSegment: true
      }));

      console.log(`处理段落 ${segmentId}, 时长: ${duration}ms`);

      // 创建FormData对象
      const formData = new FormData();
      formData.append('audio', audioBlob, 'segment.wav');
      formData.append('format', 'wav');
      formData.append('sampleRate', '16000');

      // 发送到阿里云进行转录
      const response = await fetch('/api/speech', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (result.success && result.text) {
        console.log(`段落 ${segmentId} 转录成功:`, result.text);

        // 更新段落转录结果
        set((state) => ({
          segments: state.segments.map(segment =>
            segment.id === segmentId
              ? { ...segment, transcription: result.text, isProcessing: false }
              : segment
          )
        }));

        // 发送到翻译服务
        const targetLanguage = useTranslationStore.getState().targetLanguage;
        const translationResponse = await fetch('/api/translation', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            text: result.text,
            sourceLanguage: 'zh', // 假设源语言为中文
            targetLanguage: 'en', // 假设目标语言为英文
            autoDetect: true
          })
        });

        const translationResult = await translationResponse.json();

        if (translationResult.success) {
          console.log(`段落 ${segmentId} 翻译成功:`, translationResult.translatedText);

          // 更新段落翻译结果
          set((state) => ({
            segments: state.segments.map(segment =>
              segment.id === segmentId
                ? { ...segment, translation: translationResult.translatedText }
                : segment
            ),
            isProcessingSegment: false
          }));
        } else {
          // 翻译失败
          console.error(`段落 ${segmentId} 翻译失败:`, translationResult.message);
          set((state) => ({
            segments: state.segments.map(segment =>
              segment.id === segmentId
                ? { ...segment, error: `翻译失败: ${translationResult.message}` }
                : segment
            ),
            isProcessingSegment: false
          }));
        }
      } else {
        // 转录失败
        console.error(`段落 ${segmentId} 转录失败:`, result.message);
        set((state) => ({
          segments: state.segments.map(segment =>
            segment.id === segmentId
              ? { ...segment, error: `转录失败: ${result.message}`, isProcessing: false }
              : segment
          ),
          isProcessingSegment: false
        }));
      }
    } catch (error) {
      console.error('处理段落错误:', error);
      set({ isProcessingSegment: false });
    }
  },
}));
