/**
 * 翻译状态管理
 */

import { create } from 'zustand';
import { SUPPORTED_LANGUAGES } from '@/services/deepseek/translation';

// 翻译状态接口
interface TranslationState {
  // 翻译状态
  isTranslating: boolean;
  sourceText: string;
  translatedText: string;
  sourceLanguage: string;
  targetLanguage: string;
  detectedLanguage: string | null;
  autoDetectLanguage: boolean;
  error: string | null;
  
  // 翻译历史
  history: {
    id: string;
    sourceText: string;
    translatedText: string;
    sourceLanguage: string;
    targetLanguage: string;
    timestamp: number;
  }[];
  
  // 操作方法
  setSourceText: (text: string) => void;
  setSourceLanguage: (language: string) => void;
  setTargetLanguage: (language: string) => void;
  setAutoDetectLanguage: (autoDetect: boolean) => void;
  translate: () => Promise<void>;
  clearTranslation: () => void;
  clearHistory: () => void;
  swapLanguages: () => void;
}

// 创建翻译状态存储
export const useTranslationStore = create<TranslationState>((set, get) => ({
  // 初始状态
  isTranslating: false,
  sourceText: '',
  translatedText: '',
  sourceLanguage: 'zh', // 默认源语言为中文
  targetLanguage: 'en', // 默认目标语言为英语
  detectedLanguage: null,
  autoDetectLanguage: true, // 默认自动检测语言
  error: null,
  history: [],
  
  // 设置源文本
  setSourceText: (text: string) => {
    set({ sourceText: text });
  },
  
  // 设置源语言
  setSourceLanguage: (language: string) => {
    set({ sourceLanguage: language });
  },
  
  // 设置目标语言
  setTargetLanguage: (language: string) => {
    set({ targetLanguage: language });
  },
  
  // 设置是否自动检测语言
  setAutoDetectLanguage: (autoDetect: boolean) => {
    set({ autoDetectLanguage: autoDetect });
  },
  
  // 执行翻译
  translate: async () => {
    const { sourceText, sourceLanguage, targetLanguage, autoDetectLanguage } = get();
    
    // 检查源文本是否为空
    if (!sourceText.trim()) {
      set({ error: '请输入要翻译的文本' });
      return;
    }
    
    // 检查源语言和目标语言是否相同
    if (sourceLanguage === targetLanguage && !autoDetectLanguage) {
      set({ error: '源语言和目标语言不能相同' });
      return;
    }
    
    try {
      // 开始翻译
      set({ isTranslating: true, error: null });
      
      // 调用翻译API
      const response = await fetch('/api/translation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: sourceText,
          sourceLanguage: autoDetectLanguage ? null : sourceLanguage,
          targetLanguage,
          autoDetect: autoDetectLanguage,
        }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        // 翻译成功
        const translatedText = data.translatedText;
        const detectedLanguage = data.detectedLanguage || null;
        
        // 更新状态
        set({ 
          translatedText, 
          detectedLanguage,
          error: null,
        });
        
        // 添加到历史记录
        const { history } = get();
        const newHistory = [
          {
            id: Date.now().toString(),
            sourceText,
            translatedText,
            sourceLanguage: detectedLanguage || sourceLanguage,
            targetLanguage,
            timestamp: Date.now(),
          },
          ...history,
        ].slice(0, 10); // 只保留最近10条记录
        
        set({ history: newHistory });
      } else {
        // 翻译失败
        set({ error: data.message || '翻译失败' });
      }
    } catch (error) {
      // 处理错误
      set({ error: (error as Error).message || '翻译请求失败' });
    } finally {
      // 结束翻译
      set({ isTranslating: false });
    }
  },
  
  // 清除翻译结果
  clearTranslation: () => {
    set({
      sourceText: '',
      translatedText: '',
      detectedLanguage: null,
      error: null,
    });
  },
  
  // 清除历史记录
  clearHistory: () => {
    set({ history: [] });
  },
  
  // 交换源语言和目标语言
  swapLanguages: () => {
    const { sourceLanguage, targetLanguage, autoDetectLanguage } = get();
    
    // 如果启用了自动检测，则禁用它
    if (autoDetectLanguage) {
      set({
        sourceLanguage: targetLanguage,
        targetLanguage: sourceLanguage,
        autoDetectLanguage: false,
      });
    } else {
      set({
        sourceLanguage: targetLanguage,
        targetLanguage: sourceLanguage,
      });
    }
    
    // 清除翻译结果
    set({
      sourceText: '',
      translatedText: '',
      detectedLanguage: null,
      error: null,
    });
  },
}));

// 导出支持的语言列表
export { SUPPORTED_LANGUAGES };
