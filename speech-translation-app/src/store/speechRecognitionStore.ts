/**
 * 语音识别状态管理
 */

import { create } from 'zustand';

// 语音识别状态接口
interface SpeechRecognitionState {
  // 识别状态
  isRecognizing: boolean;
  recognitionResult: string;
  error: string | null;
  
  // 操作方法
  setRecognizing: (isRecognizing: boolean) => void;
  setRecognitionResult: (result: string) => void;
  setError: (error: string | null) => void;
  clearRecognitionResult: () => void;
}

// 创建语音识别状态存储
export const useSpeechRecognitionStore = create<SpeechRecognitionState>((set) => ({
  // 初始状态
  isRecognizing: false,
  recognitionResult: '',
  error: null,
  
  // 设置识别状态
  setRecognizing: (isRecognizing: boolean) => {
    set({ isRecognizing });
  },
  
  // 设置识别结果
  setRecognitionResult: (result: string) => {
    set({ recognitionResult: result });
  },
  
  // 设置错误信息
  setError: (error: string | null) => {
    set({ error });
  },
  
  // 清除识别结果
  clearRecognitionResult: () => {
    set({ recognitionResult: '', error: null });
  },
}));
