/**
 * 音频处理工具函数
 */

/**
 * 将AudioBuffer转换为WAV格式的Blob
 * 
 * @param audioBuffer Web Audio API的AudioBuffer对象
 * @returns WAV格式的Blob对象
 */
export function audioBufferToWav(audioBuffer: AudioBuffer): Blob {
  const numOfChannels = audioBuffer.numberOfChannels;
  const length = audioBuffer.length * numOfChannels * 2; // 16-bit samples
  const sampleRate = audioBuffer.sampleRate;
  
  // 创建WAV文件头
  const buffer = new ArrayBuffer(44 + length);
  const view = new DataView(buffer);
  
  // RIFF标识
  writeString(view, 0, 'RIFF');
  // 文件长度
  view.setUint32(4, 36 + length, true);
  // WAVE标识
  writeString(view, 8, 'WAVE');
  // fmt子块标识
  writeString(view, 12, 'fmt ');
  // 子块长度
  view.setUint32(16, 16, true);
  // 音频格式（1表示PCM）
  view.setUint16(20, 1, true);
  // 通道数
  view.setUint16(22, numOfChannels, true);
  // 采样率
  view.setUint32(24, sampleRate, true);
  // 字节率 = 采样率 * 通道数 * 每样本字节数
  view.setUint32(28, sampleRate * numOfChannels * 2, true);
  // 块对齐 = 通道数 * 每样本字节数
  view.setUint16(32, numOfChannels * 2, true);
  // 位深度
  view.setUint16(34, 16, true);
  // data子块标识
  writeString(view, 36, 'data');
  // 数据长度
  view.setUint32(40, length, true);
  
  // 写入音频数据
  const channels = [];
  for (let i = 0; i < numOfChannels; i++) {
    channels.push(audioBuffer.getChannelData(i));
  }
  
  let offset = 44;
  for (let i = 0; i < audioBuffer.length; i++) {
    for (let channel = 0; channel < numOfChannels; channel++) {
      // 将-1.0 - 1.0的浮点数转换为16位整数
      const sample = Math.max(-1, Math.min(1, channels[channel][i]));
      const value = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
      view.setInt16(offset, value, true);
      offset += 2;
    }
  }
  
  return new Blob([buffer], { type: 'audio/wav' });
}

/**
 * 将Blob对象转换为AudioBuffer
 * 
 * @param blob 音频Blob对象
 * @returns Promise<AudioBuffer>
 */
export async function blobToAudioBuffer(blob: Blob): Promise<AudioBuffer> {
  const arrayBuffer = await blob.arrayBuffer();
  const audioContext = new (window.AudioContext || window.webkitAudioContext)();
  return await audioContext.decodeAudioData(arrayBuffer);
}

/**
 * 将音频Blob转换为WAV格式
 * 
 * @param blob 音频Blob对象
 * @param options 转换选项
 * @returns Promise<Blob> WAV格式的Blob对象
 */
export async function convertToWav(
  blob: Blob, 
  options: { 
    sampleRate?: number, 
    numChannels?: number 
  } = {}
): Promise<Blob> {
  // 默认选项
  const sampleRate = options.sampleRate || 16000;
  const numChannels = options.numChannels || 1;
  
  try {
    // 将Blob转换为AudioBuffer
    const audioBuffer = await blobToAudioBuffer(blob);
    
    // 创建离线AudioContext进行重采样和通道转换
    const offlineContext = new OfflineAudioContext(
      numChannels,
      audioBuffer.duration * sampleRate,
      sampleRate
    );
    
    // 创建音频源
    const source = offlineContext.createBufferSource();
    source.buffer = audioBuffer;
    
    // 如果需要将立体声转换为单声道
    if (numChannels === 1 && audioBuffer.numberOfChannels > 1) {
      // 创建通道合并器
      const merger = offlineContext.createChannelMerger(1);
      source.connect(merger);
      merger.connect(offlineContext.destination);
    } else {
      source.connect(offlineContext.destination);
    }
    
    // 开始渲染
    source.start(0);
    const renderedBuffer = await offlineContext.startRendering();
    
    // 转换为WAV格式
    return audioBufferToWav(renderedBuffer);
  } catch (error) {
    console.error('音频转换失败:', error);
    throw error;
  }
}

/**
 * 辅助函数：将字符串写入DataView
 */
function writeString(view: DataView, offset: number, string: string): void {
  for (let i = 0; i < string.length; i++) {
    view.setUint8(offset + i, string.charCodeAt(i));
  }
}
