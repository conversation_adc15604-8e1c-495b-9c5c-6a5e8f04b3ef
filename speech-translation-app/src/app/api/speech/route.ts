import { NextRequest, NextResponse } from 'next/server';
import { recognizeUtterance, validateConfig } from '@/services/aliyun';

/**
 * 语音识别API
 * 接收音频文件并返回识别结果
 */
export async function POST(request: NextRequest) {
  console.log('===== 接收到语音识别请求 =====');
  try {
    // 验证阿里云配置
    if (!validateConfig()) {
      console.error('阿里云配置不完整');
      return NextResponse.json(
        { success: false, message: '服务配置不完整，请检查环境变量' },
        { status: 500 }
      );
    }

    // 获取FormData
    const formData = await request.formData();
    const audioFile = formData.get('audio') as File;

    if (!audioFile) {
      console.error('没有接收到音频文件');
      return NextResponse.json(
        { success: false, message: '没有接收到音频文件' },
        { status: 400 }
      );
    }

    // 获取音频格式和采样率
    const format = (formData.get('format') as string) || 'wav';
    const sampleRate = parseInt((formData.get('sampleRate') as string) || '16000', 10);

    // 记录音频信息
    console.log('接收到音频文件:');
    console.log('- 文件名:', audioFile.name);
    console.log('- 文件大小:', audioFile.size, '字节');
    console.log('- 文件类型:', audioFile.type);
    console.log('- 指定格式:', format);
    console.log('- 指定采样率:', sampleRate);

    // 获取文件内容
    const buffer = Buffer.from(await audioFile.arrayBuffer());
    console.log('- 转换后Buffer大小:', buffer.length, '字节');

    // 调用阿里云语音识别
    console.log('调用阿里云语音识别API...');
    const result = await recognizeUtterance(buffer, {
      format,
      sampleRate,
      enablePunctuation: true,
      enableITN: true,
    });

    // 记录完整响应
    console.log('阿里云API响应:', JSON.stringify(result, null, 2));

    // 返回识别结果
    if (result.success) {
      console.log('识别成功, 返回结果给客户端');
      return NextResponse.json({
        success: true,
        text: result.text,
        confidence: result.confidence,
        duration: result.duration,
        requestId: result.requestId,
        emotionTag: result.emotionTag,
        emotionConfidence: result.emotionConfidence
      });
    } else {
      console.error('识别失败, 返回错误给客户端:', result.errorMessage);
      return NextResponse.json(
        {
          success: false,
          message: result.errorMessage || '识别失败',
          requestId: result.requestId
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('语音识别处理错误:', error);
    return NextResponse.json(
      { success: false, message: '语音识别处理失败', error: (error as Error).message },
      { status: 500 }
    );
  } finally {
    console.log('===== 语音识别请求处理完成 =====');
  }
}
