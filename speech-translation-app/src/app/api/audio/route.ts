import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';

// 确保上传目录存在
const ensureUploadDir = async () => {
  const uploadDir = join(process.cwd(), 'uploads');
  if (!existsSync(uploadDir)) {
    await mkdir(uploadDir, { recursive: true });
  }
  return uploadDir;
};

export async function POST(request: NextRequest) {
  try {
    // 获取FormData
    const formData = await request.formData();
    const audioFile = formData.get('audio') as File;
    
    if (!audioFile) {
      return NextResponse.json(
        { success: false, message: '没有接收到音频文件' },
        { status: 400 }
      );
    }

    // 获取文件内容
    const buffer = Buffer.from(await audioFile.arrayBuffer());
    
    // 生成唯一文件名
    const filename = `${uuidv4()}-${audioFile.name}`;
    
    // 确保上传目录存在
    const uploadDir = await ensureUploadDir();
    const filePath = join(uploadDir, filename);
    
    // 写入文件
    await writeFile(filePath, buffer);
    
    // 返回成功响应
    return NextResponse.json({
      success: true,
      message: '文件上传成功',
      filename,
      originalname: audioFile.name,
      mimetype: audioFile.type,
      size: audioFile.size,
      downloadUrl: `/api/audio/${filename}`,
    });
  } catch (error) {
    console.error('上传处理错误:', error);
    return NextResponse.json(
      { success: false, message: '文件上传处理失败', error: (error as Error).message },
      { status: 500 }
    );
  }
}
