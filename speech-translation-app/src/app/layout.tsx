import type { Metadata } from "next";
import { Toaster } from "sonner";
import "./globals.css";

export const metadata: Metadata = {
  title: "语音识别翻译系统",
  description: "基于阿里云语音识别和DeepSeek翻译的同声传译系统",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <body
        className="antialiased"
      >
        {children}
        <Toaster position="top-center" />
      </body>
    </html>
  );
}
