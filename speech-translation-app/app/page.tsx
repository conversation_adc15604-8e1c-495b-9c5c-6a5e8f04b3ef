import { Suspense } from "react";
import { AudioRecorderWrapper } from "@/components/AudioRecorderWrapper";
import { AudioUploadTest } from "@/components/AudioUploadTest";
import { AudioTestMetrics } from "@/components/AudioTestMetrics";
import { Metadata } from 'next';

// 页面元数据
export const metadata: Metadata = {
  title: '同声传译应用 - 语音识别与翻译',
  description: '基于阿里云语音识别和DeepSeek翻译的同声传译系统',
};

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen">
      <header className="border-b">
        <div className="container mx-auto py-4">
          <h1 className="text-2xl font-bold text-center">同声传译应用</h1>
          <nav className="flex justify-center space-x-4 mt-2">
            <a href="/" className="text-blue-500 hover:underline">首页</a>
            <a href="/recognition" className="text-blue-500 hover:underline">语音识别</a>
            <a href="/translation" className="text-blue-500 hover:underline">文本翻译</a>
          </nav>
        </div>
      </header>

      <main className="flex-1 container mx-auto py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <Suspense fallback={<div>加载录音组件中...</div>}>
            <AudioRecorderWrapper />
          </Suspense>

          <Suspense fallback={<div>加载上传测试组件中...</div>}>
            <AudioUploadTest />
          </Suspense>

          <Suspense fallback={<div>加载测试指标组件中...</div>}>
            <AudioTestMetrics />
          </Suspense>
        </div>
      </main>

      <footer className="border-t py-4">
        <div className="container mx-auto text-center text-sm text-gray-500">
          基于阿里云语音识别和DeepSeek翻译的同声传译系统
        </div>
      </footer>


    </div>
  );
}
