/**
 * 翻译页面
 */

import React from 'react';
import TranslationPanel from '@/components/TranslationPanel';
import TranslationHistory from '@/components/TranslationHistory';
import { Metadata } from 'next';

// 页面元数据
export const metadata: Metadata = {
  title: '文本翻译 - 同声传译应用',
  description: '使用DeepSeek AI进行高质量文本翻译',
};

/**
 * 翻译页面组件
 */
export default function TranslationPage() {
  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold text-center mb-6">文本翻译</h1>
      
      <div className="max-w-4xl mx-auto">
        <TranslationPanel />
        <TranslationHistory />
      </div>
    </div>
  );
}
