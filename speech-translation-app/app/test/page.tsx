'use client';

import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';

export default function TestPage() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [recording, setRecording] = useState<boolean>(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  // 测试基本 API
  const handleTestBasicApi = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/test');
      const data = await response.json();

      setResult(JSON.stringify(data, null, 2));
    } catch (error) {
      console.error('测试失败:', error);
      setError(`测试失败: ${(error as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  // 开始录音
  const startRecording = async () => {
    try {
      audioChunksRef.current = [];
      setError(null);
      setAudioBlob(null);
      setResult('');

      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
        setAudioBlob(audioBlob);

        // 关闭音频流
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start();
      setRecording(true);
    } catch (error) {
      console.error('录音失败:', error);
      setError(`录音失败: ${(error as Error).message}`);
    }
  };

  // 停止录音
  const stopRecording = () => {
    if (mediaRecorderRef.current && recording) {
      mediaRecorderRef.current.stop();
      setRecording(false);
    }
  };

  // 发送音频进行识别
  const recognizeSpeech = async () => {
    if (!audioBlob) {
      setError('没有可用的音频数据');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const formData = new FormData();
      formData.append('audio', audioBlob, 'recording.wav');
      formData.append('format', 'wav');
      formData.append('sampleRate', '16000');

      const response = await fetch('/api/speech', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();
      setResult(JSON.stringify(data, null, 2));
    } catch (error) {
      console.error('识别失败:', error);
      setError(`识别失败: ${(error as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">API 测试页面</h1>

      <div className="mb-6 space-y-4">
        <h2 className="text-xl font-semibold">基本 API 测试</h2>
        <Button onClick={handleTestBasicApi} disabled={loading}>
          {loading ? '测试中...' : '测试基本 API'}
        </Button>
      </div>

      <div className="mb-6 space-y-4">
        <h2 className="text-xl font-semibold">语音识别测试</h2>
        <div className="flex space-x-4">
          <Button
            onClick={startRecording}
            disabled={recording || loading}
            variant="outline"
          >
            开始录音
          </Button>

          <Button
            onClick={stopRecording}
            disabled={!recording || loading}
            variant="outline"
          >
            停止录音
          </Button>

          <Button
            onClick={recognizeSpeech}
            disabled={!audioBlob || loading || recording}
          >
            {loading ? '识别中...' : '识别语音'}
          </Button>
        </div>

        {audioBlob && (
          <div className="p-4 bg-gray-50 rounded">
            <h3 className="text-md font-medium mb-2">录音文件</h3>
            <audio controls src={URL.createObjectURL(audioBlob)} className="w-full" />
          </div>
        )}
      </div>

      {error && (
        <div className="p-4 mb-4 bg-red-100 text-red-700 rounded">
          {error}
        </div>
      )}

      {result && (
        <div className="p-4 bg-gray-100 rounded">
          <h2 className="text-lg font-semibold mb-2">测试结果:</h2>
          <pre className="whitespace-pre-wrap">{result}</pre>
        </div>
      )}
    </div>
  );
}
