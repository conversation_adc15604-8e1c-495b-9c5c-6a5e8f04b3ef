import { NextRequest, NextResponse } from 'next/server';
import { readFile, stat } from 'fs/promises';
import { existsSync } from 'fs';
import { join } from 'path';

export async function GET(
  request: NextRequest,
  { params }: { params: { filename: string } }
) {
  try {
    // 使用解构赋值来避免警告
    const filename = params?.filename;
    const filePath = join(process.cwd(), 'uploads', filename);

    // 检查文件是否存在
    if (!existsSync(filePath)) {
      return NextResponse.json(
        { success: false, message: '文件不存在' },
        { status: 404 }
      );
    }

    // 获取文件信息
    const fileStats = await stat(filePath);

    // 读取文件
    const fileBuffer = await readFile(filePath);

    // 确定MIME类型
    let contentType = 'audio/wav';
    if (filename.endsWith('.mp3')) {
      contentType = 'audio/mpeg';
    } else if (filename.endsWith('.ogg')) {
      contentType = 'audio/ogg';
    }

    // 返回文件
    return new NextResponse(fileBuffer, {
      headers: {
        'Content-Type': contentType,
        'Content-Length': fileStats.size.toString(),
        'Content-Disposition': `inline; filename="${filename}"`,
      },
    });
  } catch (error) {
    console.error('文件获取错误:', error);
    return NextResponse.json(
      { success: false, message: '文件获取失败', error: (error as Error).message },
      { status: 500 }
    );
  }
}
