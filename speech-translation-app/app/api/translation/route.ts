/**
 * 翻译API路由
 */

import { NextRequest, NextResponse } from 'next/server';
import { translateText, validateConfig, detectLanguage } from '@/services/deepseek/translation';

/**
 * 处理翻译请求
 */
export async function POST(request: NextRequest) {
  console.log('===== 接收到翻译请求 =====');
  try {
    // 验证DeepSeek配置
    if (!validateConfig()) {
      console.error('DeepSeek配置不完整');
      return NextResponse.json(
        { success: false, message: '服务配置不完整，请检查环境变量' },
        { status: 500 }
      );
    }

    // 解析请求体
    const body = await request.json();
    const { text, sourceLanguage, targetLanguage, autoDetect } = body;

    if (!text) {
      console.error('没有接收到翻译文本');
      return NextResponse.json(
        { success: false, message: '没有接收到翻译文本' },
        { status: 400 }
      );
    }

    console.log('翻译请求信息:');
    console.log('- 原文长度:', text.length);
    console.log('- 源语言:', sourceLanguage || '自动检测');
    console.log('- 目标语言:', targetLanguage);
    console.log('- 自动检测:', autoDetect ? '是' : '否');

    // 如果需要自动检测语言
    let actualSourceLanguage = sourceLanguage;
    if (autoDetect || !sourceLanguage) {
      console.log('自动检测语言...');
      actualSourceLanguage = await detectLanguage(text);
      console.log('检测到的语言:', actualSourceLanguage);
    }

    // 调用翻译服务
    console.log('调用DeepSeek翻译API...');
    const result = await translateText(text, {
      sourceLanguage: actualSourceLanguage,
      targetLanguage,
    });

    // 记录完整响应
    console.log('DeepSeek API响应:', JSON.stringify(result, null, 2));

    // 返回翻译结果
    if (result.success) {
      console.log('翻译成功, 返回结果给客户端');
      return NextResponse.json({
        success: true,
        translatedText: result.translatedText,
        detectedLanguage: actualSourceLanguage !== sourceLanguage ? actualSourceLanguage : undefined,
        requestId: result.requestId,
      });
    } else {
      console.error('翻译失败, 返回错误给客户端:', result.errorMessage);
      return NextResponse.json(
        {
          success: false,
          message: result.errorMessage || '翻译失败',
          requestId: result.requestId,
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('翻译处理错误:', error);
    return NextResponse.json(
      { success: false, message: '翻译处理失败', error: (error as Error).message },
      { status: 500 }
    );
  } finally {
    console.log('===== 翻译请求处理完成 =====');
  }
}
