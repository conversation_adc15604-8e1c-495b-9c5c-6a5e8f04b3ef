/**
 * 测试阿里云Token获取
 * 
 * 使用方法：
 * node scripts/test-aliyun-token.js
 */

// 加载环境变量
require('dotenv').config({ path: '.env.local' });

const RPCClient = require('@alicloud/pop-core');

// 阿里云配置
const config = {
  accessKeyId: process.env.ALIYUN_ACCESS_KEY_ID,
  accessKeySecret: process.env.ALIYUN_ACCESS_KEY_SECRET,
  endpoint: 'https://nls-meta.cn-shanghai.aliyuncs.com',
  apiVersion: '2019-02-28',
};

// 创建客户端
const client = new RPCClient({
  accessKeyId: config.accessKeyId,
  accessKeySecret: config.accessKeySecret,
  endpoint: config.endpoint,
  apiVersion: config.apiVersion,
});

// 测试Token获取
async function testTokenCreation() {
  console.log('测试阿里云Token获取...');
  console.log('配置信息:', {
    accessKeyId: config.accessKeyId ? '已设置' : '未设置',
    accessKeySecret: config.accessKeySecret ? '已设置' : '未设置',
    endpoint: config.endpoint,
    apiVersion: config.apiVersion,
  });
  
  try {
    const response = await client.request('CreateToken', {}, {
      method: 'POST',
    });
    
    console.log('Token获取成功!');
    console.log('Token ID:', response.Token.Id);
    console.log('过期时间:', new Date(Date.now() + response.Token.ExpireTime * 1000).toLocaleString());
    
    return response.Token.Id;
  } catch (error) {
    console.error('Token获取失败:', error);
    process.exit(1);
  }
}

// 执行测试
testTokenCreation()
  .then(() => {
    console.log('测试完成');
    process.exit(0);
  })
  .catch(error => {
    console.error('测试失败:', error);
    process.exit(1);
  });
