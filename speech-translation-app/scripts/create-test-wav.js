/**
 * 创建测试WAV文件
 * 
 * 使用方法：
 * node scripts/create-test-wav.js
 */

const fs = require('fs');
const path = require('path');

// 创建WAV文件
function createWavFile(filePath, options = {}) {
  // 默认选项
  const sampleRate = options.sampleRate || 16000;
  const numChannels = options.numChannels || 1;
  const bitsPerSample = options.bitsPerSample || 16;
  const duration = options.duration || 1; // 秒
  
  // 计算数据大小
  const numSamples = sampleRate * duration;
  const dataSize = numSamples * numChannels * (bitsPerSample / 8);
  const fileSize = 44 + dataSize;
  
  // 创建Buffer
  const buffer = Buffer.alloc(fileSize);
  
  // 写入WAV文件头
  // RIFF标识
  buffer.write('RIFF', 0);
  // 文件长度
  buffer.writeUInt32LE(36 + dataSize, 4);
  // WAVE标识
  buffer.write('WAVE', 8);
  // fmt子块标识
  buffer.write('fmt ', 12);
  // 子块长度
  buffer.writeUInt32LE(16, 16);
  // 音频格式（1表示PCM）
  buffer.writeUInt16LE(1, 20);
  // 通道数
  buffer.writeUInt16LE(numChannels, 22);
  // 采样率
  buffer.writeUInt32LE(sampleRate, 24);
  // 字节率 = 采样率 * 通道数 * 每样本字节数
  buffer.writeUInt32LE(sampleRate * numChannels * (bitsPerSample / 8), 28);
  // 块对齐 = 通道数 * 每样本字节数
  buffer.writeUInt16LE(numChannels * (bitsPerSample / 8), 32);
  // 位深度
  buffer.writeUInt16LE(bitsPerSample, 34);
  // data子块标识
  buffer.write('data', 36);
  // 数据长度
  buffer.writeUInt32LE(dataSize, 40);
  
  // 写入音频数据（生成一个简单的正弦波）
  let offset = 44;
  const amplitude = 32760; // 16位音频的最大振幅
  const frequency = 440; // A4音符的频率
  
  for (let i = 0; i < numSamples; i++) {
    // 生成正弦波
    const t = i / sampleRate;
    const value = Math.floor(amplitude * Math.sin(2 * Math.PI * frequency * t));
    
    // 写入每个通道的样本
    for (let channel = 0; channel < numChannels; channel++) {
      buffer.writeInt16LE(value, offset);
      offset += 2;
    }
  }
  
  // 写入文件
  fs.writeFileSync(filePath, buffer);
  
  console.log(`已创建WAV文件: ${filePath}`);
  console.log(`- 采样率: ${sampleRate} Hz`);
  console.log(`- 通道数: ${numChannels}`);
  console.log(`- 位深度: ${bitsPerSample} bits`);
  console.log(`- 时长: ${duration} 秒`);
  console.log(`- 文件大小: ${buffer.length} 字节`);
}

// 创建测试WAV文件
const testWavPath = path.join(__dirname, '..', 'uploads', 'test-sine-wave.wav');
createWavFile(testWavPath, {
  sampleRate: 16000,
  numChannels: 1,
  bitsPerSample: 16,
  duration: 3 // 3秒的音频
});
