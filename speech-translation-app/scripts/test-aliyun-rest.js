/**
 * 测试阿里云RESTful API
 * 
 * 使用方法：
 * node scripts/test-aliyun-rest.js
 */

// 加载环境变量
require('dotenv').config({ path: '.env.local' });
const fs = require('fs');
const path = require('path');
const fetch = require('node-fetch');
const RPCClient = require('@alicloud/pop-core');

// 阿里云配置
const config = {
  accessKeyId: process.env.ALIYUN_ACCESS_KEY_ID,
  accessKeySecret: process.env.ALIYUN_ACCESS_KEY_SECRET,
  appKey: process.env.ALIYUN_APPKEY,
  endpoint: 'https://nls-meta.cn-shanghai.aliyuncs.com',
  apiVersion: '2019-02-28',
  restEndpoint: `https://nls-gateway-${process.env.ALIYUN_API_REGION || 'cn-shanghai'}.aliyuncs.com/stream/v1/asr`
};

// 创建RPC客户端
const client = new RPCClient({
  accessKeyId: config.accessKeyId,
  accessKeySecret: config.accessKeySecret,
  endpoint: config.endpoint,
  apiVersion: config.apiVersion,
});

// 获取Token
async function getToken() {
  console.log('获取Token...');
  
  try {
    const response = await client.request('CreateToken', {}, {
      method: 'POST',
    });
    
    console.log('Token获取成功!');
    console.log('Token ID:', response.Token.Id);
    console.log('过期时间:', new Date(Date.now() + response.Token.ExpireTime * 1000).toLocaleString());
    
    return response.Token.Id;
  } catch (error) {
    console.error('Token获取失败:', error);
    process.exit(1);
  }
}

// 测试RESTful API
async function testRestfulApi() {
  console.log('测试阿里云RESTful API...');
  console.log('RESTful端点:', config.restEndpoint);
  
  try {
    // 获取Token
    const token = await getToken();
    
    // 使用上传目录中的音频文件
    const uploadsDir = path.join(process.cwd(), 'uploads');
    
    if (!fs.existsSync(uploadsDir)) {
      console.error('上传目录不存在:', uploadsDir);
      console.log('创建上传目录...');
      fs.mkdirSync(uploadsDir, { recursive: true });
    }
    
    // 查找上传目录中的WAV文件
    const files = fs.readdirSync(uploadsDir);
    const wavFiles = files.filter(file => file.endsWith('.wav'));
    
    if (wavFiles.length === 0) {
      console.error('没有找到WAV文件在上传目录中:', uploadsDir);
      console.log('请先录制一个音频文件，然后再运行测试');
      process.exit(1);
    }
    
    // 使用最新的WAV文件
    const latestWavFile = wavFiles[wavFiles.length - 1];
    const testAudioPath = path.join(uploadsDir, latestWavFile);
    console.log('使用音频文件:', testAudioPath);
    
    const audioData = fs.readFileSync(testAudioPath);
    console.log('已读取测试音频文件，大小:', audioData.length, '字节');
    
    // 构建URL参数
    let url = config.restEndpoint;
    url += `?appkey=${config.appKey}`;
    url += `&format=wav`;
    url += `&sample_rate=16000`;
    url += `&enable_punctuation_prediction=true`;
    url += `&enable_inverse_text_normalization=true`;
    
    console.log('请求URL:', url);
    
    // 发送POST请求
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'X-NLS-Token': token,
        'Content-Type': 'application/octet-stream',
      },
      body: audioData,
    });
    
    // 解析响应
    const result = await response.json();
    console.log('响应结果:', JSON.stringify(result, null, 2));
    
    if (response.ok && result.status === 20000000) {
      console.log('识别成功!');
      console.log('识别结果:', result.result);
      return result;
    } else {
      console.error('识别失败:', result.message || result.status);
      return null;
    }
  } catch (error) {
    console.error('测试失败:', error);
    process.exit(1);
  }
}

// 执行测试
testRestfulApi()
  .then(() => {
    console.log('测试完成');
    process.exit(0);
  })
  .catch(error => {
    console.error('测试失败:', error);
    process.exit(1);
  });
