/**
 * 测试DeepSeek翻译API
 * 
 * 使用方法：
 * node scripts/test-deepseek-translation.js
 */

// 加载环境变量
require('dotenv').config({ path: '.env.local' });
const axios = require('axios');

// DeepSeek配置
const DEEPSEEK_CONFIG = {
  apiKey: process.env.DEEPSEEK_API_KEY,
  baseUrl: process.env.DEEPSEEK_API_BASE_URL || 'https://api.deepseek.com',
  model: 'deepseek-chat',
};

// 测试文本
const TEST_TEXTS = [
  {
    text: '你好，世界！这是一个测试。',
    sourceLanguage: 'zh',
    targetLanguage: 'en',
    description: '中文 -> 英文'
  },
  {
    text: 'Hello, world! This is a test.',
    sourceLanguage: 'en',
    targetLanguage: 'zh',
    description: '英文 -> 中文'
  },
  {
    text: '人工智能正在改变我们的生活方式，从语音识别到自动驾驶，从医疗诊断到金融分析，AI技术的应用无处不在。',
    sourceLanguage: 'zh',
    targetLanguage: 'en',
    description: '中文长句 -> 英文'
  }
];

// 构建翻译提示词
function buildTranslationPrompt(text, sourceLanguage, targetLanguage) {
  const sourceLangName = getLanguageName(sourceLanguage);
  const targetLangName = getLanguageName(targetLanguage);
  
  return `请将以下${sourceLangName}文本翻译成${targetLangName}：\n\n${text}\n\n只需返回翻译结果，不要添加任何解释或额外内容。`;
}

// 获取语言名称
function getLanguageName(langCode) {
  const LANGUAGE_NAMES = {
    'zh': '中文',
    'en': '英语',
    'ja': '日语',
    'ko': '韩语',
    'fr': '法语',
    'de': '德语',
    'es': '西班牙语',
    'ru': '俄语',
  };
  
  return LANGUAGE_NAMES[langCode] || langCode;
}

// 测试翻译
async function testTranslation(text, sourceLanguage, targetLanguage, description) {
  console.log(`\n===== 测试 ${description} =====`);
  console.log(`原文: ${text}`);
  
  try {
    // 构建提示词
    const prompt = buildTranslationPrompt(text, sourceLanguage, targetLanguage);
    
    // 调用DeepSeek API
    console.log('调用DeepSeek API...');
    const response = await axios.post(
      `${DEEPSEEK_CONFIG.baseUrl}/v1/chat/completions`,
      {
        model: DEEPSEEK_CONFIG.model,
        messages: [
          {
            role: 'system',
            content: '你是一个专业的翻译助手，请将用户提供的文本翻译成目标语言，保持原文的意思、风格和格式。只返回翻译结果，不要添加任何解释或额外内容。',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.3,
        max_tokens: 2000,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${DEEPSEEK_CONFIG.apiKey}`,
        },
      }
    );
    
    // 解析响应
    const result = response.data;
    const translatedText = result.choices[0]?.message?.content?.trim();
    
    console.log(`翻译结果: ${translatedText}`);
    console.log('翻译成功!');
    
    return {
      success: true,
      translatedText,
      requestId: result.id,
    };
  } catch (error) {
    console.error('翻译失败:', error.response?.data || error.message);
    return {
      success: false,
      errorMessage: error.response?.data?.error?.message || error.message,
    };
  }
}

// 主函数
async function main() {
  console.log('===== DeepSeek翻译API测试 =====');
  console.log('API密钥:', DEEPSEEK_CONFIG.apiKey ? `${DEEPSEEK_CONFIG.apiKey.substring(0, 5)}...` : '未设置');
  console.log('API端点:', DEEPSEEK_CONFIG.baseUrl);
  console.log('模型:', DEEPSEEK_CONFIG.model);
  
  // 验证配置
  if (!DEEPSEEK_CONFIG.apiKey) {
    console.error('错误: DeepSeek API密钥未设置，请在.env.local文件中设置DEEPSEEK_API_KEY');
    process.exit(1);
  }
  
  // 测试所有文本
  for (const test of TEST_TEXTS) {
    await testTranslation(test.text, test.sourceLanguage, test.targetLanguage, test.description);
  }
  
  console.log('\n===== 测试完成 =====');
}

// 执行主函数
main().catch(error => {
  console.error('测试过程中发生错误:', error);
  process.exit(1);
});
