/**
 * 测试翻译API
 * 
 * 使用方法：
 * node scripts/test-translation-api.js
 */

// 加载环境变量
require('dotenv').config({ path: '.env.local' });
const axios = require('axios');

// 测试文本
const TEST_TEXTS = [
  {
    text: '你好，世界！这是一个测试。',
    sourceLanguage: 'zh',
    targetLanguage: 'en',
    autoDetect: false,
    description: '中文 -> 英文 (指定源语言)'
  },
  {
    text: 'Hello, world! This is a test.',
    sourceLanguage: null,
    targetLanguage: 'zh',
    autoDetect: true,
    description: '英文 -> 中文 (自动检测源语言)'
  },
  {
    text: '人工智能正在改变我们的生活方式，从语音识别到自动驾驶，从医疗诊断到金融分析，AI技术的应用无处不在。',
    sourceLanguage: 'zh',
    targetLanguage: 'en',
    autoDetect: false,
    description: '中文长句 -> 英文'
  }
];

// 测试翻译API
async function testTranslationAPI(text, sourceLanguage, targetLanguage, autoDetect, description) {
  console.log(`\n===== 测试 ${description} =====`);
  console.log(`原文: ${text}`);
  console.log(`源语言: ${sourceLanguage || '自动检测'}`);
  console.log(`目标语言: ${targetLanguage}`);
  
  try {
    // 调用翻译API
    console.log('调用翻译API...');
    const response = await axios.post('http://localhost:3000/api/translation', {
      text,
      sourceLanguage,
      targetLanguage,
      autoDetect,
    });
    
    const data = response.data;
    
    if (data.success) {
      console.log('翻译成功!');
      console.log(`翻译结果: ${data.translatedText}`);
      
      if (data.detectedLanguage) {
        console.log(`检测到的语言: ${data.detectedLanguage}`);
      }
      
      if (data.requestId) {
        console.log(`请求ID: ${data.requestId}`);
      }
    } else {
      console.error('翻译失败:', data.message);
    }
  } catch (error) {
    console.error('请求错误:', error.response?.data || error.message);
  }
}

// 主函数
async function main() {
  console.log('===== 翻译API测试 =====');
  
  // 测试所有文本
  for (const test of TEST_TEXTS) {
    await testTranslationAPI(
      test.text,
      test.sourceLanguage,
      test.targetLanguage,
      test.autoDetect,
      test.description
    );
  }
  
  console.log('\n===== 测试完成 =====');
}

// 执行主函数
main().catch(error => {
  console.error('测试过程中发生错误:', error);
  process.exit(1);
});
