/**
 * 测试语音识别 API
 * 
 * 使用方法：
 * node scripts/test-speech-api.js
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const FormData = require('form-data');

async function testSpeechApi() {
  try {
    console.log('测试语音识别 API...');
    
    // 使用上传目录中的音频文件
    const uploadsDir = path.join(process.cwd(), 'uploads');
    
    if (!fs.existsSync(uploadsDir)) {
      console.error('上传目录不存在:', uploadsDir);
      console.log('创建上传目录...');
      fs.mkdirSync(uploadsDir, { recursive: true });
    }
    
    // 查找上传目录中的WAV文件
    const files = fs.readdirSync(uploadsDir);
    const wavFiles = files.filter(file => file.endsWith('.wav'));
    
    if (wavFiles.length === 0) {
      console.error('没有找到WAV文件在上传目录中:', uploadsDir);
      console.log('请先录制一个音频文件，然后再运行测试');
      process.exit(1);
    }
    
    // 使用最新的WAV文件
    const latestWavFile = wavFiles[wavFiles.length - 1];
    const testAudioPath = path.join(uploadsDir, latestWavFile);
    console.log('使用音频文件:', testAudioPath);
    
    const audioData = fs.readFileSync(testAudioPath);
    console.log('已读取测试音频文件，大小:', audioData.length, '字节');
    
    // 创建 FormData
    const formData = new FormData();
    formData.append('audio', audioData, 'recording.wav');
    formData.append('format', 'wav');
    formData.append('sampleRate', '16000');
    
    // 发送请求到本地 API
    console.log('发送请求到 API...');
    const response = await axios.post('http://localhost:3000/api/speech', formData, {
      headers: {
        ...formData.getHeaders(),
      },
    });
    
    console.log('API 响应状态码:', response.status);
    console.log('API 响应数据:', JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error('测试失败:', error);
    if (error.response) {
      console.error('错误状态码:', error.response.status);
      console.error('错误数据:', error.response.data);
    }
    process.exit(1);
  }
}

// 执行测试
testSpeechApi()
  .then(() => {
    console.log('测试完成');
    process.exit(0);
  })
  .catch(error => {
    console.error('测试失败:', error);
    process.exit(1);
  });
