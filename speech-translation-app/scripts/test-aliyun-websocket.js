/**
 * 测试阿里云WebSocket连接
 *
 * 使用方法：
 * node scripts/test-aliyun-websocket.js
 */

// 加载环境变量
require('dotenv').config({ path: '.env.local' });
const WebSocket = require('ws');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const RPCClient = require('@alicloud/pop-core');

// 阿里云配置
const config = {
  accessKeyId: process.env.ALIYUN_ACCESS_KEY_ID,
  accessKeySecret: process.env.ALIYUN_ACCESS_KEY_SECRET,
  appKey: process.env.ALIYUN_APPKEY,
  endpoint: 'https://nls-meta.cn-shanghai.aliyuncs.com',
  apiVersion: '2019-02-28',
  wsEndpoint: `wss://nls-gateway-${process.env.ALIYUN_API_REGION || 'cn-shanghai'}.aliyuncs.com/ws/v1`
};

// 创建RPC客户端
const client = new RPCClient({
  accessKeyId: config.accessKeyId,
  accessKeySecret: config.accessKeySecret,
  endpoint: config.endpoint,
  apiVersion: config.apiVersion,
});

// 获取Token
async function getToken() {
  console.log('获取Token...');

  try {
    const response = await client.request('CreateToken', {}, {
      method: 'POST',
    });

    console.log('Token获取成功!');
    console.log('Token ID:', response.Token.Id);
    console.log('过期时间:', new Date(Date.now() + response.Token.ExpireTime * 1000).toLocaleString());

    return response.Token.Id;
  } catch (error) {
    console.error('Token获取失败:', error);
    process.exit(1);
  }
}

// 测试WebSocket连接
async function testWebSocketConnection() {
  console.log('测试阿里云WebSocket连接...');
  console.log('WebSocket端点:', config.wsEndpoint);

  try {
    // 获取Token
    const token = await getToken();

    // 使用上传目录中的音频文件
    const uploadsDir = path.join(process.cwd(), 'uploads');

    if (!fs.existsSync(uploadsDir)) {
      console.error('上传目录不存在:', uploadsDir);
      console.log('创建上传目录...');
      fs.mkdirSync(uploadsDir, { recursive: true });
    }

    // 查找上传目录中的WAV文件
    const files = fs.readdirSync(uploadsDir);
    const wavFiles = files.filter(file => file.endsWith('.wav'));

    if (wavFiles.length === 0) {
      console.error('没有找到WAV文件在上传目录中:', uploadsDir);
      console.log('请先录制一个音频文件，然后再运行测试');
      process.exit(1);
    }

    // 使用最新的WAV文件
    const latestWavFile = wavFiles[wavFiles.length - 1];
    const testAudioPath = path.join(uploadsDir, latestWavFile);
    console.log('使用音频文件:', testAudioPath);

    const audioData = fs.readFileSync(testAudioPath);
    console.log('已读取测试音频文件，大小:', audioData.length, '字节');

    return new Promise((resolve, reject) => {
      // 创建WebSocket连接
      const ws = new WebSocket(config.wsEndpoint);

      // 生成唯一消息ID
      const messageId = uuidv4().replace(/-/g, '');

      // 连接打开时发送识别请求
      ws.on('open', () => {
        console.log('WebSocket连接已打开');

        try {
          // 准备请求参数
          const params = {
            header: {
              message_id: messageId,
              namespace: 'SpeechRecognizer',
              name: 'StartRecognition',
              appkey: config.appKey,
              token
            },
            payload: {
              format: 'wav',
              sample_rate: 16000,
              enable_punctuation_prediction: true,
              enable_inverse_text_normalization: true,
            }
          };

          console.log('发送识别请求...');
          ws.send(JSON.stringify(params));

          console.log('发送音频数据...');
          ws.send(audioData);

          // 发送结束指令
          const stopParams = {
            header: {
              message_id: uuidv4().replace(/-/g, ''),
              namespace: 'SpeechRecognizer',
              name: 'StopRecognition',
              appkey: config.appKey,
              token
            },
            payload: {}
          };

          console.log('发送结束指令...');
          ws.send(JSON.stringify(stopParams));
        } catch (error) {
          console.error('发送请求失败:', error);
          ws.close();
          reject(error);
        }
      });

      // 接收服务器响应
      ws.on('message', (data) => {
        try {
          const response = JSON.parse(data.toString());
          console.log('收到响应:', JSON.stringify(response, null, 2));

          // 处理识别完成消息
          if (response.header &&
              response.header.name === 'RecognitionCompleted' &&
              response.header.status === 20000000) {

            console.log('识别完成!');
            console.log('识别结果:', response.payload.result);

            if (response.payload.emo_tag) {
              console.log('情感分析:', response.payload.emo_tag);
              console.log('情感置信度:', response.payload.emo_confidence);
            }

            // 关闭WebSocket连接
            ws.close();
            resolve(response);
          }

          // 处理错误消息
          if (response.header && response.header.status !== 20000000 && response.header.status !== 0) {
            console.error('识别失败:', response.header.status_text || response.header.status);

            // 关闭WebSocket连接
            ws.close();
            reject(new Error(response.header.status_text || response.header.status));
          }
        } catch (error) {
          console.error('解析响应失败:', error, data.toString());
        }
      });

      // 错误处理
      ws.on('error', (error) => {
        console.error('WebSocket错误:', error);
        reject(error);
      });

      // 连接关闭
      ws.on('close', () => {
        console.log('WebSocket连接已关闭');
      });

      // 设置超时
      setTimeout(() => {
        if (ws.readyState === WebSocket.OPEN) {
          console.error('识别超时');
          ws.close();
          reject(new Error('识别超时'));
        }
      }, 30000); // 30秒超时
    });
  } catch (error) {
    console.error('测试失败:', error);
    process.exit(1);
  }
}

// 执行测试
testWebSocketConnection()
  .then(() => {
    console.log('测试完成');
    process.exit(0);
  })
  .catch(error => {
    console.error('测试失败:', error);
    process.exit(1);
  });
