/**
 * 使用正弦波测试阿里云语音识别API
 * 
 * 使用方法：
 * node scripts/test-with-sine-wave.js
 */

// 加载环境变量
require('dotenv').config({ path: '.env.local' });
const fs = require('fs');
const path = require('path');
const RPCClient = require('@alicloud/pop-core');

// 阿里云配置
const config = {
  accessKeyId: process.env.ALIYUN_ACCESS_KEY_ID,
  accessKeySecret: process.env.ALIYUN_ACCESS_KEY_SECRET,
  appKey: process.env.ALIYUN_APPKEY,
  endpoint: 'https://nls-meta.cn-shanghai.aliyuncs.com',
  apiVersion: '2019-02-28',
  restEndpoint: `https://nls-gateway-${process.env.ALIYUN_API_REGION || 'cn-shanghai'}.aliyuncs.com/stream/v1/asr`
};

// 创建RPC客户端
const client = new RPCClient({
  accessKeyId: config.accessKeyId,
  accessKeySecret: config.accessKeySecret,
  endpoint: config.endpoint,
  apiVersion: config.apiVersion,
});

// 获取Token
async function getToken() {
  console.log('获取Token...');
  
  try {
    const response = await client.request('CreateToken', {}, {
      method: 'POST',
    });
    
    console.log('Token获取成功!');
    console.log('Token ID:', response.Token.Id);
    console.log('过期时间:', new Date(Date.now() + response.Token.ExpireTime * 1000).toLocaleString());
    
    return response.Token.Id;
  } catch (error) {
    console.error('Token获取失败:', error);
    process.exit(1);
  }
}

// 测试RESTful API
async function testRestfulApi() {
  console.log('测试阿里云RESTful API...');
  console.log('RESTful端点:', config.restEndpoint);
  
  // 获取Token
  const token = await getToken();
  
  // 读取测试音频文件
  const testAudioPath = path.join(__dirname, '..', 'uploads', 'test-sine-wave.wav');
  console.log('使用音频文件:', testAudioPath);
  
  const audioData = fs.readFileSync(testAudioPath);
  console.log('已读取测试音频文件，大小:', audioData.length, '字节');
  
  // 检查WAV文件头
  checkWavHeader(audioData);
  
  // 构建URL参数
  let url = config.restEndpoint;
  url += `?appkey=${config.appKey}`;
  url += `&format=wav`;
  url += `&sample_rate=16000`;
  url += `&enable_punctuation_prediction=true`;
  url += `&enable_inverse_text_normalization=true`;
  
  console.log('请求URL:', url);
  
  // 发送POST请求
  const fetch = require('node-fetch');
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'X-NLS-Token': token,
      'Content-Type': 'application/octet-stream',
    },
    body: audioData,
  });
  
  const result = await response.json();
  console.log('响应结果:', JSON.stringify(result, null, 2));
  
  if (result.status === 20000000) {
    console.log('识别成功!');
    console.log('识别结果:', result.result);
  } else {
    console.error('识别失败:', result.message || result.status);
  }
  
  console.log('测试完成');
}

// 检查WAV文件头
function checkWavHeader(buffer) {
  if (buffer.length < 44) {
    console.error('WAV文件头不完整，长度小于44字节');
    return false;
  }
  
  // 检查RIFF头
  const riff = buffer.slice(0, 4).toString('ascii');
  const wave = buffer.slice(8, 12).toString('ascii');
  const fmt = buffer.slice(12, 16).toString('ascii');
  
  console.log('WAV文件头标识:', { riff, wave, fmt });
  
  if (riff !== 'RIFF' || wave !== 'WAVE' || fmt !== 'fmt ') {
    console.error('WAV文件头标识不正确:', { riff, wave, fmt });
    return false;
  }
  
  // 读取音频格式信息
  const audioFormat = buffer.readUInt16LE(20);      // 1表示PCM
  const numChannels = buffer.readUInt16LE(22);      // 通道数
  const sampleRate = buffer.readUInt32LE(24);       // 采样率
  const byteRate = buffer.readUInt32LE(28);         // 字节率
  const blockAlign = buffer.readUInt16LE(32);       // 块对齐
  const bitsPerSample = buffer.readUInt16LE(34);    // 位深度
  
  console.log('WAV文件头信息:');
  console.log('- 音频格式:', audioFormat, audioFormat === 1 ? '(PCM)' : '(非PCM)');
  console.log('- 通道数:', numChannels, numChannels === 1 ? '(单声道)' : '(多声道)');
  console.log('- 采样率:', sampleRate, 'Hz');
  console.log('- 字节率:', byteRate, 'bytes/sec');
  console.log('- 块对齐:', blockAlign, 'bytes');
  console.log('- 位深度:', bitsPerSample, 'bits');
  
  return true;
}

// 执行测试
testRestfulApi();
