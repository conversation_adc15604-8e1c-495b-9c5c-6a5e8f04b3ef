{"name": "speech-translation-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@alicloud/pop-core": "^1.8.0", "@chakra-ui/react": "^3.16.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "form-data": "^4.0.2", "framer-motion": "^12.8.2", "lucide-react": "^0.503.0", "next": "15.3.1", "next-themes": "^0.4.6", "node-fetch": "^2.7.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.8", "uuid": "^11.1.0", "ws": "^8.18.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "dotenv": "^16.5.0", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "typescript": "^5"}}